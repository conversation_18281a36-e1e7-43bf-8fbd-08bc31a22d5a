FROM hub-mirror.wps.cn/sreopen/nginx:1.20

#这将nginx配置模板复制过去
COPY pdfcv_auth_nginx.template /etc/nginx/conf.d
EXPOSE  80 443
WORKDIR /etc/nginx/conf.d

#添加环境变量的写入
# 依赖于kae传入
# rabbitmq
ARG RABBITMQ_HOST=***********
ARG RABBITMQ_PORT=15672
ENV PCV_RABBITMQ_HOST=${RABBITMQ_HOST}
ENV PCV_RABBITMQ_PORT=${RABBITMQ_PORT}

# rabbitmq_hotbackup
ARG RABBITMQ_HOTBACKUP_HOST=************
ARG RABBITMQ_HOTBACKUP_PORT=15672
ENV PCV_RABBITMQ_HOTBACKUP_HOST=${RABBITMQ_HOTBACKUP_HOST}
ENV PCV_RABBITMQ_HOTBACKUP_PORT=${RABBITMQ_HOTBACKUP_PORT}

# rabbitmq_hotbackup2
ARG RABBITMQ_HOTBACKUP2_HOST=***********
ARG RABBITMQ_HOTBACKUP2_PORT=15672
ENV PCV_RABBITMQ_HOTBACKUP2_HOST=${RABBITMQ_HOTBACKUP2_HOST}
ENV PCV_RABBITMQ_HOTBACKUP2_PORT=${RABBITMQ_HOTBACKUP2_PORT}

# rabbitmq_database
ARG RABBITMQ_DATABASE_HOST=************
ARG RABBITMQ_DATABASE_PORT=15672
ENV PCV_RABBITMQ_DATABASE_HOST=${RABBITMQ_DATABASE_HOST}
ENV PCV_RABBITMQ_DATABASE_PORT=${RABBITMQ_DATABASE_PORT}
# rabbitmq_chatfile-prod
ARG RABBITMQ_CHATFILE_HOST=***********
ARG RABBITMQ_CHATFILE_PORT=15672
ENV PCV_RABBITMQ_CHATFILE_HOST=${RABBITMQ_CHATFILE_HOST}
ENV PCV_RABBITMQ_CHATFILE_PORT=${RABBITMQ_CHATFILE_PORT}
# zhumo-prod-rabbit-layout-master
ARG RABBITMQ_LAYOUT_MASTER_HOST=***********
ARG RABBITMQ_LAYOUT_MASTER_PORT=15672
ENV PCV_RABBITMQ_LAYOUT_MASTER_HOST=${RABBITMQ_LAYOUT_MASTER_HOST}
ENV PCV_RABBITMQ_LAYOUT_MASTER_PORT=${RABBITMQ_LAYOUT_MASTER_PORT}
# zhumo-prod-rabbit-layout-extend
ARG RABBITMQ_LAYOUT_EXTEND_HOST=************
ARG RABBITMQ_LAYOUT_EXTEND_PORT=15672
ENV PCV_RABBITMQ_LAYOUT_EXTEND_HOST=${RABBITMQ_LAYOUT_EXTEND_HOST}
ENV PCV_RABBITMQ_LAYOUT_EXTEND_PORT=${RABBITMQ_LAYOUT_EXTEND_PORT}
# zhumo-prod-rabbit-layout-backup
ARG RABBITMQ_LAYOUT_BACKUP_HOST=***********
ARG RABBITMQ_LAYOUT_BACKUP_PORT=15672
ENV PCV_RABBITMQ_LAYOUT_BACKUP_HOST=${RABBITMQ_LAYOUT_BACKUP_HOST}
ENV PCV_RABBITMQ_LAYOUT_BACKUP_PORT=${RABBITMQ_LAYOUT_BACKUP_PORT}

# grafana
ARG GRAFANA_HOST=pdf-cv-grafana.monitoring
ARG GRAFANA_PORT=3000
ENV PCV_GRAFANA_HOST=${GRAFANA_HOST}
ENV PCV_GRAFANA_PORT=${GRAFANA_PORT}

# konga
ARG KONGA_HOST=pdf-cv-konga.kong
ARG KONGA_PORT=1337
ENV PCV_KONGA_HOST=${KONGA_HOST}
ENV PCV_KONGA_PORT=${KONGA_PORT}

# konga
ARG KLOG_HOST=**************
ARG KLOG_PORT=5601
ENV PCV_KLOG_HOST=${KLOG_HOST}
ENV PCV_KLOG_PORT=${KLOG_PORT}

# 管理平台
ARG ADMIN_HOST=***********
ARG ADMIN_PORT=32277
ENV PCV_ADMIN_HOST=${ADMIN_HOST}
ENV PCV_ADMIN_PORT=${ADMIN_PORT}

ARG ADMIN_KSO_HOST=***********
ARG ADMIN_KSO_PORT=32277
ENV PCV_ADMIN_KSO_HOST=${ADMIN_KSO_HOST}
ENV PCV_ADMIN_KSO_PORT=${ADMIN_KSO_PORT}


ENTRYPOINT envsubst \
      '${PCV_RABBITMQ_HOST} ${PCV_RABBITMQ_PORT} \
       ${PCV_RABBITMQ_HOTBACKUP_HOST} ${PCV_RABBITMQ_HOTBACKUP_PORT} \
       ${PCV_RABBITMQ_HOTBACKUP2_HOST} ${PCV_RABBITMQ_HOTBACKUP2_PORT} \
       ${PCV_RABBITMQ_DATABASE_HOST} ${PCV_RABBITMQ_DATABASE_PORT} \
       ${PCV_RABBITMQ_CHATFILE_HOST} ${PCV_RABBITMQ_CHATFILE_PORT} \
       ${PCV_RABBITMQ_LAYOUT_MASTER_HOST} ${PCV_RABBITMQ_LAYOUT_MASTER_PORT} \
       ${PCV_RABBITMQ_LAYOUT_EXTEND_HOST} ${PCV_RABBITMQ_LAYOUT_EXTEND_PORT} \
       ${PCV_RABBITMQ_LAYOUT_BACKUP_HOST} ${PCV_RABBITMQ_LAYOUT_BACKUP_PORT} \
       ${PCV_GRAFANA_HOST} ${PCV_GRAFANA_PORT} \
       ${PCV_KONGA_HOST} ${PCV_KONGA_PORT} \
       ${PCV_KLOG_HOST} ${PCV_KLOG_PORT} \
       ${PCV_ADMIN_HOST} ${PCV_ADMIN_PORT} \
       ${PCV_ADMIN_KSO_HOST} ${PCV_ADMIN_KSO_PORT} \
       ' < pdfcv_auth_nginx.template > default.conf && cat default.conf &&  nginx -g 'daemon off;'