FROM hub-mirror.wps.cn/sreopen/nginx:1.20

#这将nginx配置模板复制过去
COPY pdfcv_auth_nginx.template /etc/nginx/conf.d
EXPOSE  80 443
WORKDIR /etc/nginx/conf.d

#添加环境变量的写入
# 依赖于kae传入

#rabbitmq
ARG RABBITMQ_HOST=***********
ARG RABBITMQ_PORT=15672
ENV PCV_RABBITMQ_HOST=${RABBITMQ_HOST}
ENV PCV_RABBITMQ_PORT=${RABBITMQ_PORT}

#es
ARG ES_HOST=*************
ARG ES_PORT=9200
ENV PCV_ES_HOST=${ES_HOST}
ENV PCV_ES_PORT=${ES_PORT}

#grafana
ARG GRAFANA_HOST=***********
ARG GRAFANA_PORT=31356
ENV PCV_GRAFANA_HOST=${GRAFANA_HOST}
ENV PCV_GRAFANA_PORT=${GRAFANA_PORT}

#konga
ARG KONGA_HOST=************
ARG KONGA_PORT=1337
ENV PCV_KONGA_HOST=${KONGA_HOST}
ENV PCV_KONGA_PORT=${KONGA_PORT}

#klog
ARG KLOG_HOST=*************
ARG KLOG_PORT=5601
ENV PCV_KLOG_HOST=${KLOG_HOST}
ENV PCV_KLOG_PORT=${KLOG_PORT}

# 管理平台
ARG ADMIN_HOST=************
ARG ADMIN_PORT=80
ENV PCV_ADMIN_HOST=${ADMIN_HOST}
ENV PCV_ADMIN_PORT=${ADMIN_PORT}

# Jaeger
ARG JAEGER_HOST=************
ARG JAEGER_PORT=80
ENV PCV_JAEGER_HOST=${JAEGER_HOST}
ENV PCV_JAEGER_PORT=${JAEGER_PORT}

ARG ADMIN_KSO_HOST=************
ARG ADMIN_KSO_PORT=80
ENV PCV_ADMIN_KSO_HOST=${ADMIN_KSO_HOST}
ENV PCV_ADMIN_KSO_PORT=${ADMIN_KSO_PORT}

ENTRYPOINT envsubst \
      '${PCV_RABBITMQ_HOST} ${PCV_RABBITMQ_PORT} \
       ${PCV_ES_HOST} ${PCV_ES_PORT} \
       ${PCV_GRAFANA_HOST} ${PCV_GRAFANA_PORT} \
       ${PCV_KONGA_HOST} ${PCV_KONGA_PORT} \
       ${PCV_KLOG_HOST} ${PCV_KLOG_PORT} \
       ${PCV_ADMIN_HOST} ${PCV_ADMIN_PORT} \
       ${PCV_JAEGER_HOST} ${PCV_JAEGER_PORT} \
       ${PCV_ADMIN_KSO_HOST} ${PCV_ADMIN_KSO_PORT} \
       ' < pdfcv_auth_nginx.template > default.conf && cat default.conf &&  nginx -g 'daemon off;'