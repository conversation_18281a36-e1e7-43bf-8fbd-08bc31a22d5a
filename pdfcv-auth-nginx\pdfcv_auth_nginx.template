upstream rabbitmq{
  server ${PCV_RABBITMQ_HOST}:${PCV_RABBITMQ_PORT};
}
upstream es{
  server ${PCV_ES_HOST}:${PCV_ES_PORT};
}
upstream grafana{
  server ${PCV_GRAFANA_HOST}:${PCV_GRAFANA_PORT};
}
upstream konga{
  server ${PCV_KONGA_HOST}:${PCV_KONGA_PORT};
}
upstream klog{
  server ${PCV_KLOG_HOST}:${PCV_KLOG_PORT};
}
upstream admin{
  server ${PCV_ADMIN_HOST}:${PCV_ADMIN_PORT};
}
upstream jaeger{
  server ${PCV_JAEGER_HOST}:${PCV_JAEGER_PORT};
}
# 定义上游服务器
upstream goadmin{
  server ${PCV_ADMIN_KSO_HOST}:${PCV_ADMIN_KSO_PORT};
}
map $http_host $backend {
    pcv-test-int-rabbitmq.wps.cn rabbitmq;
    pcv-test-int-es.wps.cn es;
    pcv-test-int-grafana.wps.cn grafana;
    pcv-test-int-konga.wps.cn konga;
    pcv-test-int-klog.wps.cn klog;
    pcv-test-admin.wps.cn admin;
    pcv-test-int-jaeger.wps.cn jaeger;
    pcv-test-admin.kso.net goadmin;
}
server {
    listen 80;
    server_name ~^(pcv-test-int-(rabbitmq|es|grafana|konga|klog|admin|jaeger))\.wps\.cn$ 
                 ~^pcv-test-admin\.kso\.net$;
    # send all requests to the `/auth` endpoint for authorization
    auth_request /auth;

    # 认证请求
    location = /auth {
        proxy_pass https://pcv-test-auth.wps.cn/api/v1/auth;
        proxy_pass_request_body off;
        proxy_set_header Content-Length "";
    }

    # 错误页面处理
    error_page 401 = @error401;

    location @error401 {
        return 302 https://pcv-test-auth.wps.cn/api/v1/Login?url=$scheme://$host$request_uri;
    }

    # 代理请求到相应的服务
    location / {
        proxy_pass http://$backend;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
  }