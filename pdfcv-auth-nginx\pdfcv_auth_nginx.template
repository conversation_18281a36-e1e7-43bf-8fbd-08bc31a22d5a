upstream rabbitmq_prod{
  server ${PCV_RABBITMQ_HOST}:${PCV_RABBITMQ_PORT};
}
upstream hotbackup_prod{
  server ${PCV_RABBITMQ_HOTBACKUP_HOST}:${PCV_RABBITMQ_HOTBACKUP_PORT};
}
upstream hotbackup2_prod{
  server ${PCV_RABBITMQ_HOTBACKUP2_HOST}:${PCV_RABBITMQ_HOTBACKUP2_PORT};
}
upstream database_prod{
  server ${PCV_RABBITMQ_DATABASE_HOST}:${PCV_RABBITMQ_DATABASE_PORT};
}
upstream chatfile_prod {
  server ${PCV_RABBITMQ_CHATFILE_HOST}:${PCV_RABBITMQ_CHATFILE_PORT};
}

upstream layout_master_prod {
  server ${PCV_RABBITMQ_LAYOUT_MASTER_HOST}:${PCV_RABBITMQ_LAYOUT_MASTER_PORT};
}
upstream layout_extend_prod {
  server ${PCV_RABBITMQ_LAYOUT_EXTEND_HOST}:${PCV_RABBITMQ_LAYOUT_EXTEND_PORT};
}
upstream layout_backup_prod {
  server ${PCV_RABBITMQ_LAYOUT_BACKUP_HOST}:${PCV_RABBITMQ_LAYOUT_BACKUP_PORT};
}
upstream grafana_prod{
  server ${PCV_GRAFANA_HOST}:${PCV_GRAFANA_PORT};
}
upstream konga_prod{
  server ${PCV_KONGA_HOST}:${PCV_KONGA_PORT};
}
upstream klog_prod{
  server ${PCV_KLOG_HOST}:${PCV_KLOG_PORT};
}
upstream admin{
  server ${PCV_ADMIN_HOST}:${PCV_ADMIN_PORT};
}
upstream goadmin{
  server ${PCV_ADMIN_KSO_HOST}:${PCV_ADMIN_KSO_PORT};
}
map $http_host $backend {
    pcv-int-rabbitmq.wps.cn rabbitmq_prod;
    pcv-int-rabbitmq-database.wps.cn database_prod;
    pcv-int-rabbitmq-chatfile.wps.cn chatfile_prod;
    pcv-int-rabbitmq-hotbackup.wps.cn hotbackup_prod;
    pcv-int-rabbitmq-hotbackup2.wps.cn hotbackup2_prod;
    pcv-int-rabbitmq-layout-master-prod.wps.cn layout_master_prod;
    pcv-int-rabbitmq-layout-extend-prod.wps.cn layout_extend_prod;
    pcv-int-rabbitmq-layout-backup-prod.wps.cn layout_backup_prod;
    pcv-int-grafana.wps.cn grafana_prod;
    pcv-int-konga.wps.cn konga_prod;
    pcv-int-klog.wps.cn klog_prod;
    pcv-admin.wps.cn admin;
    pcv-int-konga-kaeshare.wps.cn konga_prod;
    pcv-admin.kso.net goadmin;
}

server {
    listen 80;
    server_name ~^(pcv-int-(klog|konga|grafana|rabbitmq|rabbitmq-hotbackup|rabbitmq-hotbackup2|rabbitmq-database|rabbitmq-chatfile|layout-master-prod|layout-extend-prod|layout-backup-prod|konga-kaeshare)|pcv-admin)\.wps\.cn$|^pcv-admin\.kso\.net$;
    # send all requests to the `/auth` endpoint for authorization
    auth_request /auth;

    location = /auth {
      # forward the /auth request to Proxy
      # proxy_set_header Host $host;   #填写OSS访问域名

      proxy_pass https://pcv-auth.wps.cn/api/v1/auth;
      # Proxy only acts on the request headers
      proxy_pass_request_body off;
      proxy_set_header Content-Length "";

    }

    # if auth returns `401 not authorized` then forward the request to the error401block
    error_page 401 = @error401;

    location @error401 {
      # redirect to Proxy for login
      return 302 https://pcv-auth.wps.cn/api/v1/Login?url=https://$http_host$request_uri;
    }
    proxy_set_header Host $http_host; # added after upgrade from 8.1 to 8.4.1 for origin not allowed error
    # proxy pass authorized requests to your service
    # 代理请求到相应的服务
    location / {
        # 转发请求到选定的 backend
        proxy_pass http://$backend;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

  }

