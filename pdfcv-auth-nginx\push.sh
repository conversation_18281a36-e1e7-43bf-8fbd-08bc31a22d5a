#!/bin/bash
VERSION=_prod
docker rmi pdfcv-auth-nginx:release${VERSION}
docker rmi hub-mirror.wps.cn/pdfcv/auth-app/pdfcv-auth-nginx:release${VERSION}
docker build -t pdfcv-auth-nginx:release${VERSION} -f Dockerfile .
docker tag  pdfcv-auth-nginx:release${VERSION} hub-mirror.wps.cn/pdfcv/auth-app/pdfcv-auth-nginx:release${VERSION}
docker push hub-mirror.wps.cn/pdfcv/auth-app/pdfcv-auth-nginx:release${VERSION}