FROM hub-mirror.wps.cn/sreopen/node:16.15 as build-stage
WORKDIR /app
COPY . .

ARG GOADMIN=https://pcv-auth.wps.cn/api/
# ARG GOADMIN=http://local-test.wps.cn/api/
# ARG WPSOAUTH='https://openapi.wps.cn/oauthapi/v2/authorize?response_type=code\&appid=AK20230202JWZHCN\&autologin=false\&redirect_uri=https://pcv-auth.wps.cn/auth-redirect\&scope=user_info'
ARG WPSOAUTH='https://portal.kwps.cn/api/access/wpsopen/oauth2/authorize?response_type=code&appid=990e02b6-65b0-11ee-aab7-0abe8d1c9619&redirect_uri=https://pcv-test-auth.wps.cn/auth-redirect&scope=user_info'
RUN sed -i "s|\${GOADMIN}|${GOADMIN}|g" .env.production \
    && sed -i "s|\${WPSOAUTH}|${WPSOAUTH}|g" .env.production \
    && cat .env.production \
    && rm -rf node_modules \
    && tar -xzf node_modules.tar.gz \
    && npm run build:prod \
    && rm -rf node_modules

# production stage
FROM hub-mirror.wps.cn/sreopen/nginx:1.20
COPY --from=build-stage /app/default.conf /etc/nginx/conf.d/default.conf
COPY --from=build-stage /app/dist /usr/share/nginx/html

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
