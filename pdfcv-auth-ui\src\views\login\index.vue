<template>
  <div class="login-container">

    <div class="login-weaper animated bounceInDown">
      <div class="login-left">
        <div class="login-time" v-text="currentTime" />
        <img src="../pic/logo_gitee_g_red.png" alt="" class="img">
        <p class="title" v-text="sysInfo.sys_app_name" />
      </div>
      <div class="login-border">
        <div class="login-main">
          <div class="login-title">用户登录</div>
          <div v-if="qrcodeBinding !== ''" class="login-qrcode">
            <div>
              <span>双因子凭证
                <el-tooltip
                  effect="dark"
                  placement="top"
                >
                  <div slot="content">
                    请使用双因子认证APP（如KMFA小程序、FreeOTP、GoogleAuthenticator等）扫描二维码来添加账号。<br>
                    此时APP将(提示生成一串6位数字的验证码，请输入验证码，点击&quot;添加&quot;完成双因子凭证添加。
                  </div>
                  <svg-icon icon-class="question" style="margin-right:0;" />
                </el-tooltip>
                :</span>
            </div>
            <div style="text-align: center;">
              <img width="200px" :src="qrcodeBinding" alt="">
            </div>
          </div>
          <el-form
            v-if="codeVerifying"
            ref="loginForm"
            :model="loginForm"
            :rules="loginRules"
            class="login-form"
            autocomplete="on"
            label-position="left"
            @submit.native.prevent
          >
            <el-form-item prop="twofactor">
              <span class="svg-container">
                <svg-icon icon-class="validCode" />
              </span>
              <el-input
                ref="twofactor"
                v-model="loginForm.twofactor"
                placeholder="双因子验证码"
                name="twofactor"
                type="text"
                tabindex="1"
                maxlength="6"
                autocomplete="off"
                @keyup.enter.native="handleVerifyCode"
              />
            </el-form-item>
            <el-button
              :loading="loading"
              type="primary"
              style="width: 100%; padding: 12px 20px; margin-bottom: 30px"
              @click.native.prevent="handleVerifyCode"
            >
              <span v-if="!loading">{{ qrcodeBinding === '' ? '验 证' : '添 加' }}</span>
              <span v-else>验 证 中...</span>
            </el-button>
          </el-form>
          <el-button
            v-if="!codeVerifying"
            :loading="loading"
            type="primary"
            style="width: 100%; padding: 12px 20px; margin-bottom: 30px"
            @click.native.prevent="handleLoginWps"
          >
            <span v-if="!loading">WPS 登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import openWindow from '@/utils/open-window'
import { accesstoken } from '@/api/user'

export default {
  name: 'Login',
  data() {
    return {
      // 双因子(totp)二维码
      qrcodeBinding: '',
      codeVerifying: false,
      loginForm: {
        externalId: '',
        twofactor: ''
      },
      loginRules: {
        twofactor: [
          { required: true, trigger: 'change', message: '验证码不能为空' }
        ]
      },
      loading: false,
      redirect: undefined,
      otherQuery: {},
      currentTime: null,
      sysInfo: ''
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  created() {
    if (window.localStorage) {
      window.localStorage.setItem('pdfcv-oauth-code', '')
    }

    this.getCurrentTime()
    this.getSystemSetting()
  },
  mounted() {
    if (this.codeVerifying === true) {
      this.$refs.twofactor.focus()
    }
    // window.addEventListener('message', this.getOauthCode)
  },
  destroyed() {
    clearInterval(this.timer)
    // window.removeEventListener('message', () => {})
  },
  methods: {
    getOauthCode() {
      // const { type, data } = event.data
      let code = ''
      if (window.localStorage) {
        code = window.localStorage.getItem('pdfcv-oauth-code')
        window.localStorage.setItem('pdfcv-oauth-code', '')
      } else {
        this.$message({
          showClose: true,
          message: '浏览器不兼容!',
          center: true,
          duration: 5000,
          type: 'error'
        })
        return
      }

      console.log('wps oauth code [' + code + ']')
      if (code !== '') {
        // accesstoken
        accesstoken(JSON.stringify({ code })
        ).then(resp => {
          // 400 || 403
          if (resp === undefined) {
            console.log('undefined resp')
            return Promise.resolve()
          }
          // 500
          if (resp.data === undefined) {
            return Promise.reject('内部错误' + resp.msg)
          }
          // 200
          const { externalId, status, image } = resp.data
          this.loginForm.externalId = externalId
          if (status === 1) { // 2FA/TFA_UNBINDED
            this.qrcodeBinding = 'data:image/png;base64,' + image
          }
          this.codeVerifying = true
        }).catch(error => {
          this.$message({
            showClose: true,
            message: '授权失败! ' + error,
            center: true,
            duration: 4000,
            type: 'error'
          })
        })
      }
    },
    getSystemSetting() {
      this.$store.dispatch('system/settingDetail').then((ret) => {
        this.sysInfo = ret
        document.title = ret.sys_app_name
      })
    },
    getCurrentTime() {
      this.timer = setInterval((_) => {
        this.currentTime = moment().format('YYYY-MM-DD HH时mm分ss秒')
      }, 1000)
    },
    async handleLoginWps() {
      this.loading = true
      const newWindow = openWindow(process.env.VUE_APP_WPS_OAUTH_API, '_blank', 0, 0)

      await new Promise((resolve, reject) => {
        var checkLoop = setInterval(function() {
          if (newWindow.closed) {
            clearInterval(checkLoop)
            resolve()
          }
        }, 1000)
      }).then(() => {
        this.getOauthCode()
      })
      this.loading = false
    },
    handleVerifyCode() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true
          this.$store
            .dispatch('user/login', this.loginForm)
            .then(() => {
              this.$router
                .push({ path: this.redirect || '/', query: this.otherQuery })
                .catch(() => {})
            })
            .catch(() => {
            })
          this.loading = false
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    }
  }
}
</script>

<style lang="scss" scoped>
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #2834436e;
$light_gray: #fff;
$cursor: #fff;

.login-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  background: url("../../assets/login.png") no-repeat;
  background-color: #0e6aff59;
  position: relative;
  background-size: cover;
  height: 100vh;
  background-position: 50%;
}

.login-weaper {
  margin: 0 auto;
  width: 1000px;
  -webkit-box-shadow: -4px 5px 10px rgba(0, 0, 0, 0.4);
  box-shadow: -4px 5px 10px rgba(0, 0, 0, 0.4);
  z-index: 1000;
}

.login-left {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  background-color: rgba(64, 158, 255, 0);
  color: #fff;
  float: left;
  width: 50%;
  position: relative;
  min-height: 500px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  .login-time {
    position: absolute;
    left: 25px;
    top: 25px;
    width: 100%;
    color: #fff;
    opacity: 0.9;
    font-size: 18px;
    overflow: hidden;
    font-weight: 500;
  }
}

.login-left .img {
  width: 300px;
  height: 90px;
  border-radius: 3px;
}

.login-left .title {
  text-align: center;
  color: #fff;
  letter-spacing: 2px;
  font-size: 16px;
  font-weight: 600;
}

.login-border {
  position: relative;
  min-height: 500px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-left: none;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  color: #fff;
  background-color: hsla(0, 0%, 100%, 0.9);
  width: 50%;
  float: left;
}

.login-main {
  margin: 0 auto;
  width: 65%;
}

.login-title {
  color: #333;
  margin-bottom: 15px;
  font-weight: 500;
  font-size: 22px;
  text-align: center;
  letter-spacing: 4px;
}
.login-qrcode {
  color: #666;
  div {
    margin-bottom: 10px;
  }
}

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  ::v-deep .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: #333;
      height: 47px;
      caret-color: #333;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.8);
    border-radius: 5px;
    color: #454545;
    margin-bottom: 10px;
  }
}
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.login-container {
  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
    .login-weaper {
      width: 100%;
      padding: 0 30px;
      box-sizing: border-box;
      box-shadow: none;
    }
    .login-main{
      width: 80%;
    }
    .login-left {
      display: none !important;
    }
    .login-border {
      width: 100%;
    }
  }
}
</style>
