package apis

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"

	"go-admin/app/admin/models"
	"go-admin/app/admin/service"
	"go-admin/app/admin/service/dto"
	"go-admin/common/actions"
	"go-admin/common/twofactor"
	"go-admin/common/utils"
	wpsOauth2 "go-admin/openwps/openapi/oauth2"
	wpsAvatar "go-admin/openwps/util/avatarutil"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-admin-team/go-admin-core/sdk/api"
)

const (
	WpsURL_IsLogin = "https://account.wps.cn/api/v3/islogin"
)

type SysOauth2 struct {
	api.Api
}

func (e SysOauth2) getAccountInfo(c *gin.Context) (result *dto.AccountInfoResp, err error) {
	// for k, v := range c.Request.Header {
	// 	e.Logger.Tracef("%s : %s", k, v[0])
	// }
	e.Logger.Tracef("client header: %v", c.Request.Header)
	// cookie
	cookie, err := c.Request.Cookie("wps_sid")
	if err != nil {
		// 未登录
		err = fmt.Errorf("get wps_sid cookie error, %s", err.Error())
		return
	}
	// user-agent
	agent := c.Request.UserAgent()
	// x-client-ip
	clientIP := c.ClientIP()
	if len(agent) == 0 || len(clientIP) == 0 {
		err = errors.New("empty User-Agent or X-Client-IP")
		return
	}

	accountRequest, err := http.NewRequest(http.MethodGet, WpsURL_IsLogin, nil)
	if err != nil {
		err = fmt.Errorf("create request failed, %v", err)
		return
	}
	accountRequest.Header.Add("Cookie", cookie.String())
	accountRequest.Header.Add("User-Agent", agent)
	accountRequest.Header.Add("X-Client-IP", clientIP)

	resp, err := utils.SendReqByReconnect(accountRequest)
	if err != nil {
		err = fmt.Errorf("get wps accountinfo error, %v", err)
		return
	}
	accountInfo, _ := ioutil.ReadAll(resp.Body)
	resp.Body.Close()
	fmt.Println("get account info, ", string(accountInfo))

	if resp.StatusCode != 200 {
		err = fmt.Errorf("send request failed, response code:%d, body:%s", resp.StatusCode, string(accountInfo))
		return
	}
	result = &dto.AccountInfoResp{}
	err = json.Unmarshal(accountInfo, result)
	return
}

// 验证是否登录
func (e SysOauth2) GetAuthTokenHandler(c *gin.Context) {
	s := service.SysOauth2{}
	sUser := service.SysUser{}
	req := dto.AccessTokenReq{}
	err := e.MakeContext(c).
		Bind(&req, binding.JSON).
		MakeService(&s.Service).
		MakeService(&sUser.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(http.StatusBadRequest, err, err.Error())
		return
	}
	cookie, err := c.Request.Cookie("pdfcv-auth-token")
	if err != nil || cookie.Value == "" {
		// 未登录
		c.JSON(http.StatusUnauthorized, gin.H{
			"code": 401,
			"msg":  "未登录",
		})
		return
	}

	e.OK(cookie.Value, "用户已登陆")
}

func (e SysOauth2) AccessTokenHandler(c *gin.Context) {
	s := service.SysOauth2{}
	sUser := service.SysUser{}
	req := dto.AccessTokenReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req, binding.JSON).
		MakeService(&s.Service).
		MakeService(&sUser.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(http.StatusBadRequest, err, err.Error())
		return
	}
	// get userinfo by account.wps
	accountInfo, err := e.getAccountInfo(c)
	e.Logger.Trace("get userinfo by account.wps, ", accountInfo)
	if err != nil {
		e.Logger.Error(err)
		e.Error(http.StatusBadRequest, err, err.Error())
		return
	}

	// get access_token
	token, err := wpsOauth2.GetToken(req.Code)
	if err != nil {
		err = errors.New(fmt.Sprintf("get wps token error, %s", err.Error()))
		e.Logger.Error(err)
		e.Error(http.StatusBadRequest, err, err.Error())
		return
	}

	// get userinfo by openapi.wps
	userInfo, err := wpsOauth2.GetUserInfoV3(token.AccessToken, token.OpenId)
	if err != nil {
		err = errors.New(fmt.Sprintf("get wps userinfo error, %s", err.Error()))
		e.Logger.Error(err)
		e.Error(http.StatusBadRequest, err, err.Error())
		return
	}
	avatar, err := wpsAvatar.NewAvatar(userInfo.Avatar)
	if err != nil {
		err = errors.New(fmt.Sprintf("invalid avatar, %s", err.Error()))
		e.Logger.Error(err)
		e.Error(http.StatusInternalServerError, err, err.Error())
		return
	}

	// externalId
	externalId := strconv.FormatInt(accountInfo.Userid, 10)

	// update table sys_oauth2
	oauth2Req := dto.SysOauth2UpdateReq{
		Id:           externalId,      // avatar.GetWpsId(),
		ExpiresIn:    token.ExpiresIn, // 1day/86400
		AccessToken:  token.AccessToken,
		RefreshToken: token.RefreshToken,
		OpenId:       token.OpenId,
		Nickname:     userInfo.Nickname,
		Avatar:       avatar.GetAvatarUrl(),
		Status:       models.UNKNOWN,
	}
	var model = models.SysOauth2{}
	err = s.Update(&oauth2Req, &model)
	// FOREIGN KEY (`id`) REFERENCES `sys_user` (`external_id`)
	if err != nil {
		e.Logger.Errorf("无权限访问, %s", err.Error())
		err = errors.New("无权限访问")
		e.Error(http.StatusForbidden, err, err.Error())
		return
	}

	resp := dto.AccessTokenResp{
		ExternalId: externalId, // saved as Cookie
		Status:     model.Status,
	}
	if model.Status != models.TFA_UNBINDED {
		e.OK(resp, "accesstoken获取成功")
		return
	}

	// get userinfo from db.sys_user by externalId
	userReq := dto.SysUserGetPageReq{
		ExternalId: externalId,
	}
	userList := make([]models.SysUser, 0)
	var userCount int64
	err = sUser.GetPage(&userReq, &actions.DefaultDataPermission, &userList, &userCount)
	if err != nil || userCount != 1 || len(userList) != 1 {
		err = errors.New(fmt.Sprintf("get userinfo error, %s", err.Error()))
		e.Logger.Error(err)
		e.Error(http.StatusInternalServerError, err, err.Error())
		return
	}

	image, err := twofactor.GetTotpQRCodeB64s(userList[0].Email, userList[0].Secret)
	if err != nil {
		e.Logger.Error("get QRCode image error, ", err)
		e.Error(http.StatusInternalServerError, err, err.Error())
		return
	}
	resp.Image = image
	e.OK(resp, "设备未绑定双因子凭证")
}
