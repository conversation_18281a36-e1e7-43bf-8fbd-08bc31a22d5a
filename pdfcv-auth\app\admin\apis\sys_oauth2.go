package apis

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"

	"go-admin/app/admin/service"
	"go-admin/app/admin/service/dto"
	"go-admin/openwps/internalapi/appinfo"
	wpsOauth2 "go-admin/openwps/openapi/oauth2"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-admin-team/go-admin-core/sdk/api"
	"ksogit.kingsoft.net/security-pub/mfasdk"
)

type SysOauth2 struct {
	api.Api
}

// 验证是否登录
func (e SysOauth2) GetAuthTokenHandler(c *gin.Context) {
	s := service.SysOauth2{}
	sUser := service.SysUser{}
	req := dto.AccessTokenReq{}
	err := e.MakeContext(c).
		Bind(&req, binding.JSON).
		MakeService(&s.Service).
		MakeService(&sUser.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(http.StatusBadRequest, err, err.Error())
		return
	}
	cookie, err := c.Request.Cookie("pdfcv-auth-token")
	if err != nil || cookie.Value == "" {
		// 未登录
		c.JSON(http.StatusUnauthorized, gin.H{
			"code": 401,
			"msg":  "未登录",
		})
		return
	}

	e.OK(cookie.Value, "用户已登陆")
}
func (e SysOauth2) AuthenticatorNew(c *gin.Context) {
	s := service.SysOauth2{}
	sUser := service.SysUser{}
	req := dto.AccessTwofactorReq{}
	err := e.MakeContext(c).
		Bind(&req, binding.JSON).
		MakeService(&s.Service).
		MakeService(&sUser.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(http.StatusBadRequest, err, err.Error())
		return
	}
	wps_uid, _ := strconv.ParseInt(req.ExternalId, 10, 64)
	// 验证双因子(totp)验证码
	ak := appinfo.GetAuth2AppId()
	sk := appinfo.GetAuth2AppKey()
	client := mfasdk.New(mfasdk.WithAKSK(ak, sk))
	// 验证动态令牌
	ok, err := client.Verify(mfasdk.VerifyParam{
		WpsUID: wps_uid,
		Code:   req.Twofactor,
	})
	if err != nil {
		err = errors.New(fmt.Sprintf("twofactor validate code, %s", err.Error()))
		e.Logger.Error(err)
		e.Error(http.StatusBadRequest, err, err.Error())
		return
	}
	if !ok {
		err = errors.New(fmt.Sprintf("invalid twofactor code, %s", err.Error()))
		e.Logger.Error(err)
		e.Error(http.StatusBadRequest, err, err.Error())
		return
	}
	resp := dto.AccessTokenResp{
		Status: 1, // saved as Cookie
	}
	e.OK(resp, "双因子验证成功")
}
func (e SysOauth2) AccessTokenHandler(c *gin.Context) {
	s := service.SysOauth2{}
	sUser := service.SysUser{}
	req := dto.AccessTokenReq{}
	err := e.MakeContext(c).
		Bind(&req, binding.JSON).
		MakeService(&s.Service).
		MakeService(&sUser.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(http.StatusBadRequest, err, err.Error())
		return
	}
	// get access_token
	token, err := wpsOauth2.GetToken(req.Code)
	if err != nil {
		err = errors.New(fmt.Sprintf("get wps token error, %s", err.Error()))
		e.Logger.Error(err)
		e.Error(http.StatusBadRequest, err, err.Error())
		return
	}
	// get userinfo by openapi.wps
	userInfo, err := wpsOauth2.GetUserInfoV3(token.AccessToken)
	if err != nil {
		err = errors.New(fmt.Sprintf("get wps userinfo error, %s", err.Error()))
		e.Logger.Error(err)
		e.Error(http.StatusBadRequest, err, err.Error())
		return
	}
	// externalId
	externalId := strconv.FormatInt(userInfo.WPSUid, 10)

	resp := dto.AccessTokenResp{
		ExternalId: externalId, // saved as Cookie
	}
	ak := appinfo.GetAuth2AppId()
	sk := appinfo.GetAuth2AppKey()
	client := mfasdk.New(mfasdk.WithAKSK(ak, sk))

	// 在xiezuo中进行动态令牌通知
	if err := client.Notice(mfasdk.NoticeParam{
		WpsUID: userInfo.WPSUid,
	}); err != nil {
		panic(err)
	}
	e.OK(resp, "accesstoken获取成功")
}
