package models

type AuthStatus int

const (
	UNKNOWN      AuthStatus = iota
	TFA_UNBINDED            // 未绑定双因子凭证到设备
	TFA_BINDED
)

type SysOauth2 struct {
	Id           string     `json:"id"`
	SysUser      SysUser    `json:"-" gorm:"foreignKey:Id;references:ExternalId;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	ExpiresIn    int64      `json:"expires_in" gorm:"type:bigint"`          // 有效期
	AccessToken  string     `json:"access_token" gorm:"type:varchar(255)"`  // accesstoken
	RefreshToken string     `json:"refresh_token" gorm:"type:varchar(255)"` // refreshtoken
	OpenId       string     `json:"openid" gorm:"type:varchar(128)"`        // openid
	Nickname     string     `json:"nickname" gorm:"type:varchar(64)"`
	Avatar       string     `json:"avatar" gorm:"type:varchar(255)"`
	Status       AuthStatus `json:"status" gorm:"size:4"` // tinyint
}

func (SysOauth2) TableName() string {
	return "sys_oauth2"
}
