package dto

import (
	"go-admin/app/admin/models"
)

type AccessTokenReq struct {
	Code string `json:"code" binding:"required"`
}

type AccessTokenResp struct {
	ExternalId string            `json:"externalId"`
	Status     models.AuthStatus `json:"status"`
	Image      string            `json:"image"`
}

type LoginReq struct {
	Id string `json:"id" binding:"required"`
}

type AccountInfoResp struct {
	Result           string `json:"result"` // ok
	Userid           int64  `json:"userid"`
	Companyid        int64  `json:"companyid"`
	CurrentCompanyid int64  `json:"current_companyid"`
	Loginmode        string `json:"loginmode"`
	IsPlus           bool   `json:"is_plus"` // true
}

// SysOauth2InsertReq 增使用的结构体
type SysOauth2InsertReq struct {
	Id           string
	ExpiresIn    int64
	AccessToken  string
	RefreshToken string
	OpenId       string
	Nickname     string
	Avatar       string
	Status       models.AuthStatus
}

func (s *SysOauth2InsertReq) Generate(model *models.SysOauth2) {
	model.Id = s.Id
	model.ExpiresIn = s.ExpiresIn
	model.AccessToken = s.AccessToken
	model.RefreshToken = s.RefreshToken
	model.OpenId = s.OpenId
	model.Nickname = s.Nickname
	model.Avatar = s.Avatar
	model.Status = s.Status
}

// GetId 获取数据对应的ID
func (s *SysOauth2InsertReq) GetId() interface{} {
	return s.Id
}

func (s *SysOauth2InsertReq) insert2UpdateReq() *SysOauth2UpdateReq {
	// return (*SysOauth2UpdateReq)(s)
	return &SysOauth2UpdateReq{
		Id:           s.Id,
		ExpiresIn:    s.ExpiresIn,
		AccessToken:  s.AccessToken,
		RefreshToken: s.RefreshToken,
		OpenId:       s.OpenId,
		Nickname:     s.Nickname,
		Avatar:       s.Avatar,
	}
}

type SysOauth2Req interface {
	Generate(model *models.SysOauth2)
	GetId() interface{}
}

// SysOauth2UpdateReq 改使用的结构体
type SysOauth2UpdateReq struct {
	Id           string
	ExpiresIn    int64
	AccessToken  string
	RefreshToken string
	OpenId       string
	Nickname     string
	Avatar       string
	Status       models.AuthStatus
}

func (s *SysOauth2UpdateReq) Generate(model *models.SysOauth2) {
	model.Id = s.Id
	model.ExpiresIn = s.ExpiresIn
	model.AccessToken = s.AccessToken
	model.RefreshToken = s.RefreshToken
	model.OpenId = s.OpenId
	model.Nickname = s.Nickname
	model.Avatar = s.Avatar
	if s.Status != models.UNKNOWN {
		// override by request
		model.Status = s.Status
	} else if model.Status == models.UNKNOWN {
		// if no record in db, initialize
		model.Status = models.TFA_UNBINDED
	} // else, keep the original value
}

func (s *SysOauth2UpdateReq) GetId() interface{} {
	return s.Id
}

// SysOauth2GetReq 获取单个的结构体
type SysOauth2GetReq struct {
	Id string
}

func (s *SysOauth2GetReq) GetId() interface{} {
	return s.Id
}

// SysOauth2DeleteReq 删除的结构体
type SysOauth2DeleteReq struct {
	Ids []string
}

func (s *SysOauth2DeleteReq) Generate(model *models.SysOauth2) {
}

func (s *SysOauth2DeleteReq) GetId() interface{} {
	return s.Ids
}

type ResetSysOauth2StatusReq struct {
	Id string
}

func (s *ResetSysOauth2StatusReq) GetId() interface{} {
	return s.Id
}

func (s *ResetSysOauth2StatusReq) Generate(model *models.SysOauth2) {
	model.Id = s.Id
	model.Status = models.TFA_UNBINDED
}
