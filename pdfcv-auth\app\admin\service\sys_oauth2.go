package service

import (
	"errors"

	"github.com/go-admin-team/go-admin-core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/admin/models"
	"go-admin/app/admin/service/dto"
)

type SysOauth2 struct {
	service.Service
}

// Get 获取SysOauth2对象
func (e *SysOauth2) Get(d *dto.SysOauth2GetReq, model *models.SysOauth2) error {
	var err error
	var data models.SysOauth2

	db := e.Orm.Model(&data).
		Where("id = ?", d.Id).
		First(model)
	err = db.Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("db error:%s", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建SysOauth2对象
func (e *SysOauth2) Insert(c *dto.SysOauth2InsertReq) error {
	var err error
	var data models.SysOauth2
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Update 修改SysOauth2对象
func (e *SysOauth2) Update(c dto.SysOauth2Req, model *models.SysOauth2) error {
	var err error
	e.Orm.Where("id = ?", c.GetId()).First(model)
	c.Generate(model)

	db := e.Orm.Save(model)
	if db.Error != nil {
		err = db.Error
		e.Log.Errorf("db error:%v", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")

	}
	return nil
}

// Remove 删除SysOauth2
func (e *SysOauth2) Remove(d *dto.SysOauth2DeleteReq) error {
	var err error
	var data models.SysOauth2

	db := e.Orm.Model(&data).
		Where("id in ?", d.Ids).Delete(&data)
	if db.Error != nil {
		err = db.Error
		e.Log.Errorf("Delete error: %s", err)
		return err
	}
	if db.RowsAffected == 0 {
		err = errors.New("无权删除该数据")
		return err
	}
	return nil
}
