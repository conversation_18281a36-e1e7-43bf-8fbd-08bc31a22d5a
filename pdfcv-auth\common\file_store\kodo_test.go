package file_store

import (
	"testing"
)

func TestKODOUpload(t *testing.T) {
	e := OXS{"", "", "", ""}
	var oxs = e.Setup(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, map[string]interface{}{"Zone": "华东"})
	err := oxs.UpLoad("test.png", "./test.png")
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	t.Log("ok")
}

func TestKODOGetTempToken(t *testing.T) {
	e := OXS{"", "", "", ""}
	var oxs = e.Setup(Qi<PERSON>iuKodo, map[string]interface{}{"Zone": "华东"})
	token, _ := oxs.GetTempToken()
	t.Log(token)
	t.Log("ok")
}
