package handler

import (
	"errors"
	"go-admin/app/admin/models"
	"go-admin/openwps/internalapi/appinfo"
	"strconv"

	"ksogit.kingsoft.net/security-pub/mfasdk"

	log "github.com/go-admin-team/go-admin-core/logger"
	"gorm.io/gorm"
)

type Login struct {
	ExternalId string `form:"ExternalId" json:"externalId" binding:"required"`
	Twofactor  string `form:"Twofactor" json:"twofactor" binding:"required"`
	Username   string // tobedel `form:"UserName" json:"username" binding:"required"`
	Code       string // tobedel `form:"Code" json:"code" binding:"required"`
	UUID       string // tobedel `form:"UUID" json:"uuid" binding:"required"`
}

func (u *Login) GetUser(tx *gorm.DB) (user models.SysUser, role models.SysRole, err error) {
	err = tx.Table("sys_user").Where("external_id = ?  and status = 2", u.ExternalId).First(&user).Error
	if err != nil {
		log.Errorf("get user error, %s", err.Error())
		return
	}
	wps_uid, _ := strconv.ParseInt(u.ExternalId, 10, 64)
	// 验证双因子(totp)验证码
	ak := appinfo.GetAuth2AppId()
	sk := appinfo.GetAuth2AppKey()
	client := mfasdk.New(mfasdk.WithAKSK(ak, sk))
	// 验证动态令牌
	ok, err := client.Verify(mfasdk.VerifyParam{
		WpsUID: wps_uid,
		Code:   u.Twofactor,
	})
	if err != nil {
		log.Errorf("twofactor validate code, %s", err.Error())
		return
	}
	if !ok {
		err = errors.New("invalid twofactor code")
		log.Error(err)
		return
	}

	err = tx.Table("sys_role").Where("role_id = ? ", user.RoleId).First(&role).Error
	if err != nil {
		log.Errorf("get role error, %s", err.Error())
		return
	}
	return
}
