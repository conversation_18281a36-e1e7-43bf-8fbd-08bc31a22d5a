package handler

import (
	"errors"
	"go-admin/app/admin/models"
	"go-admin/common/twofactor"

	log "github.com/go-admin-team/go-admin-core/logger"
	"gorm.io/gorm"
)

type Login struct {
	ExternalId string `form:"ExternalId" json:"externalId" binding:"required"`
	Twofactor  string `form:"Twofactor" json:"twofactor" binding:"required"`
	Username   string // tobedel `form:"UserName" json:"username" binding:"required"`
	Code       string // tobedel `form:"Code" json:"code" binding:"required"`
	UUID       string // tobedel `form:"UUID" json:"uuid" binding:"required"`
}

func (u *Login) GetUser(tx *gorm.DB) (user models.SysUser, role models.SysRole, err error) {
	err = tx.Table("sys_user").Where("external_id = ?  and status = 2", u.ExternalId).First(&user).Error
	if err != nil {
		log.Errorf("get user error, %s", err.Error())
		return
	}

	// 验证双因子(totp)验证码
	log.Infof("twofactor-code verification: code=%s secret=%s", u.Twofactor, user.Secret)
	valid, err := twofactor.Validate(u.Twofactor, user.Email, user.Secret)
	if err != nil {
		log.Errorf("twofactor validate code, %s", err.Error())
		return
	}
	if !valid {
		err = errors.New("invalid twofactor code")
		log.Error(err)
		return
	}

	// 更新双因子设备绑定状态
	var oauth2 models.SysOauth2
	db := tx.Table("sys_oauth2").Where("id = ? ", user.ExternalId).First(&oauth2)
	if db.Error != nil {
		log.Errorf("get oauth2 error, %s", err.Error())
		return
	}
	oauth2.Status = models.TFA_BINDED
	if db.Save(oauth2).Error != nil {
		log.Errorf("update oauth2 error, %s", err.Error())
		return
	}

	err = tx.Table("sys_role").Where("role_id = ? ", user.RoleId).First(&role).Error
	if err != nil {
		log.Errorf("get role error, %s", err.Error())
		return
	}
	return
}
