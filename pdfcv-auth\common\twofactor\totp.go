package twofactor

import (
	"bytes"
	"encoding/base64"
	"image/png"
	"time"

	"go-admin/config"

	"github.com/pquerna/otp/totp"
)

var (
	validateOtps *totp.ValidateOpts = nil
)

func getValidateOtps() *totp.ValidateOpts {
	if validateOtps == nil {
		validateOtps = &totp.ValidateOpts{
			Algorithm: config.OpenwpsConfig.Algorithm.ToOtpAlgorithm(),
			Digits:    config.OpenwpsConfig.Digits,
			Period:    config.OpenwpsConfig.Period,
			Skew:      config.OpenwpsConfig.Skew,
		}
	}
	return validateOtps
}

func GenerateOpts(accountName string) totp.GenerateOpts {
	return totp.GenerateOpts{
		AccountName: accountName,
		Algorithm:   config.OpenwpsConfig.Algorithm.ToOtpAlgorithm(),
		Digits:      config.OpenwpsConfig.Digits,
		Issuer:      config.OpenwpsConfig.Issuer,
		Period:      config.OpenwpsConfig.Period,
		SecretSize:  config.OpenwpsConfig.SecretSize,
		// Secret: []byte("A7XY5LZDE2CMCSYREYHPHKEQVSMMFTPT"),
	}
}

func NewTotpSecret(accountName string) (string, error) {
	opts := GenerateOpts(accountName)
	key, err := totp.Generate(opts)
	if err != nil {
		return "", err
	}
	return key.Secret(), nil
}

func GetTotpQRCodeB64s(accountName string, secret string) (string, error) {
	opts := GenerateOpts(accountName)
	// use user's secret
	opts.Secret = []byte(secret)
	key, err := totp.Generate(opts)
	if err != nil {
		return "", err
	}

	image, err := key.Image(200, 200)
	if err != nil {
		return "", err
	}
	var buf bytes.Buffer
	err = png.Encode(&buf, image)
	return base64.StdEncoding.EncodeToString(buf.Bytes()), nil
}

func Validate(code, accountName string, secret string) (bool, error) {
	opts := GenerateOpts(accountName)
	// use user's secret
	opts.Secret = []byte(secret)
	key, err := totp.Generate(opts)
	if err != nil {
		return false, err
	}
	return totp.ValidateCustom(code, key.Secret(), time.Now(), *getValidateOtps())
}
