package utils

import (
	"archive/zip"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"
)

func DownLoadFileCV(name string, url string) error {

	newFile, _ := os.Create("tmp/pdf_edit/" + name)
	defer newFile.Close()
	client := http.Client{Timeout: 10 * time.Second}
	response, err := client.Get(url)
	if err != nil {
		fmt.Println(err)
		return err
	}
	defer response.Body.Close()
	io.Copy(newFile, response.Body)
	return nil
}

func GetNameByUrl(url string) string {
	ix := strings.Index(url, "?")
	var fileName = ""
	if ix > 0 {
		fileName = url[strings.LastIndex(url, "/")+1 : ix]
	} else {
		fileName = url[strings.LastIndex(url, "/")+1:]
	}
	return fileName
}

func CheckUrlExptime(input string) bool {
	u, err := url.Parse(input)
	if err != nil {
		return false
	}
	urlParams := u.RawQuery
	m, err := url.ParseQuery(urlParams)
	exptime := m.Get("Expires")
	if exptime != "" {
		unix := time.Now().Unix()
		explongtime, _ := strconv.ParseInt(exptime, 10, 64)
		if explongtime < unix {
			return false
		}
	}
	return true
}

func CompressFiles(myfolder string, dest string) error {
	files, err := ioutil.ReadDir(myfolder)
	if err != nil {
		fmt.Println(err)
		return err
	}
	d, _ := os.Create(dest)
	defer d.Close()
	w := zip.NewWriter(d)
	defer w.Close()

	for _, file := range files {
		f, _ := os.Open("tmp/pdf_edit/" + file.Name())
		err := compress(f, "t", w)
		if err != nil {
			return err
		}
	}
	return nil
}

func compress(file *os.File, prefix string, zw *zip.Writer) error {
	info, err := file.Stat()
	if err != nil {
		return err
	}
	if info.IsDir() {
		prefix = prefix + "/" + info.Name()
		fileInfos, err := file.Readdir(-1)
		if err != nil {
			return err
		}
		for _, fi := range fileInfos {
			f, err := os.Open(file.Name() + "/" + fi.Name())
			if err != nil {
				return err
			}
			err = compress(f, prefix, zw)
			if err != nil {
				return err
			}
		}
	} else {
		header, err := zip.FileInfoHeader(info)
		header.Name = header.Name

		if err != nil {
			return err
		}
		writer, err := zw.CreateHeader(header)
		if err != nil {
			return err
		}
		_, err = io.Copy(writer, file)
		file.Close()
		if err != nil {
			return err
		}
	}
	return nil
}
