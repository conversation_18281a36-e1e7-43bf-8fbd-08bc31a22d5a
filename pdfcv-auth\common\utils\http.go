package utils

import (
	"net"
	"net/http"
	"time"
)

var DefaltClient = &http.Client{
	Transport: &http.Transport{
		Dial: func(netw, addr string) (net.Conn, error) {
			//设置建立连接超时
			conn, err := net.DialTimeout(netw, addr, time.Second*time.Duration(2))
			if err != nil {
				return nil, err
			}
			//设置发送接收数据超时
			conn.SetDeadline(time.Now().Add(time.Second * time.Duration(30)))
			return conn, nil
		},
	},
}

func SendReqByReconnect(request *http.Request) (*http.Response, error) {
	resp, err := DefaltClient.Do(request)
	if err == nil {
		return resp, err
	}
	//尝试重连2次
	for i := 0; i < 2; i++ {
		if oe, ok := err.(net.Error); ok {
			if oe.Timeout() {
				time.Sleep(time.Second)
				resp, err = DefaltClient.Do(request)
				if err == nil {
					return resp, err
				}
			}
		}
	}
	return resp, err
}
