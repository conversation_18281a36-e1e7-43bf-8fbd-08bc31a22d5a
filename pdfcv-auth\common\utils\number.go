package utils

import (
	"math"
	"strconv"
)

// MaxInt 取较大值
func MaxInt(args ...int) int {
	m := 0
	for i, d := range args {
		if i == 0 {
			m = d
		} else if d >= m {
			m = d
		}
	}
	return m
}

// MinInt 取较小值
func MinInt(args ...int) int {
	m := 0
	for i, d := range args {
		if i == 0 {
			m = d
		} else if d < m {
			m = d
		}
	}
	return m
}

func MinFloat64(args ...float64) float64 {
	m := math.MaxFloat64
	for i, d := range args {
		if i == 0 {
			m = d
		} else {
			m = math.Min(m, d)
		}

	}
	return m
}

func MaxFloat64(args ...float64) float64 {
	m := math.MaxFloat64
	for i, d := range args {
		if i == 0 {
			m = d
		} else {
			m = math.Max(m, d)
		}

	}
	return m
}

func RoundFloat64(num float64, decimal int) float64 {
	d := float64(1)
	if decimal > 0 {
		// 10的N次方
		d = math.Pow10(decimal)
	}
	return math.Round(num*d) / d
}

//DivideInterval divide [begin, end] into num average sub interval
func DivideInterval(begin, end, num int) [][]int {
	subIntervals := [][]int{}

	if begin > end {
		return subIntervals
	}

	cnt := end - begin + 1
	if cnt <= num {
		for i := begin; i <= end; i++ {
			subIntervals = append(subIntervals, []int{i, i})
		}
	} else {
		b := 0
		e := 0
		nextBegin := begin
		perSlice := cnt / num
		remainder := cnt % num
		for i := 1; i <= num; i++ {
			b = nextBegin
			if i <= remainder {
				e = b + perSlice
			} else {
				e = b + perSlice - 1
			}
			nextBegin = e + 1
			subIntervals = append(subIntervals, []int{b, e})
		}
	}
	return subIntervals
}

//JoinFloat64 JoinFloat64
func JoinFloat64(vals []float64, sep string, prec int) string {
	s := ""
	for _, v := range vals {
		if s != "" {
			s += sep
		}
		s += strconv.FormatFloat(v, 'f', prec, 64)
	}

	return s
}
