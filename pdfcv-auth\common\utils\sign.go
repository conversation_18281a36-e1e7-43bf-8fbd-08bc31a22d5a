package utils

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
)

//字符串转base64
func String2Base64(str string) string {
	return base64.StdEncoding.EncodeToString([]byte(str))
}

//hmac sha1 加签 ,返回byte数组
func SignatureSha1ForByte(secret string, data string) []byte {
	h := hmac.New(sha1.New, []byte(secret))
	h.Write([]byte(data))
	return h.Sum(nil)
}

//hmac sha1 加签 ,返回string
func SignatureSha1ForString(secret string, data string) string {
	h := hmac.New(sha1.New, []byte(secret))
	h.Write([]byte(data))
	return string(h.Sum(nil))
}
