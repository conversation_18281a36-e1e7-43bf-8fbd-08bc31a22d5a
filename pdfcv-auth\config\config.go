package config

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"

	"github.com/go-admin-team/go-admin-core/config"
	"github.com/go-admin-team/go-admin-core/config/source"
	log "github.com/go-admin-team/go-admin-core/logger"
)

var (
	_cfg *Settings
)

// Settings 兼容原先的配置结构
type Settings struct {
	Settings  Config `yaml:"settings"`
	callbacks []func()
}

func (e *Settings) runCallback() {
	for i := range e.callbacks {
		e.callbacks[i]()
	}
}

func (e *Settings) OnChange() {
	e.init()
	log.Info("!!! config change and reload")
}

func (e *Settings) Init() {
	e.init()
	log.Info("!!! config init")
}

func (e *Settings) init() {
	e.Settings.Logger.Setup()
	e.Settings.Openwps.Setup()
	e.Settings.multiDatabase()
	e.runCallback()
}

// Config 配置集合
type Config struct {
	Application *Application          `json:"application"`
	Ssl         *Ssl                  `json:"ssl"`
	Logger      *Logger               `json:"logger"`
	Jwt         *Jwt                  `json:"jwt"`
	Database    *Database             `json:"db"`
	Databases   *map[string]*Database `json:"dbs"`
	Gen         *Gen                  `json:"gen"`

	Extend *Extend `json:"extend"`

	Openwps *Openwps `json:"openwps"`
}

// 多db改造
func (e *Config) multiDatabase() {
	if len(*e.Databases) == 0 {
		*e.Databases = map[string]*Database{
			"*": e.Database,
		}

	}
}

// Setup 载入配置文件
func Setup(s source.Source,
	fs ...func()) {
	_cfg = &Settings{
		Settings: Config{
			Application: ApplicationConfig,
			Ssl:         SslConfig,
			Logger:      LoggerConfig,
			Jwt:         JwtConfig,
			Database:    DatabaseConfig,
			Databases:   &DatabasesConfig,
			Gen:         GenConfig,

			Extend: ExtConfig, // 注入配置扩展项

			Openwps: OpenwpsConfig,
		},
		callbacks: fs,
	}
	var err error
	config.DefaultConfig, err = config.NewConfig(
		config.WithSource(s),
		config.WithEntity(_cfg),
	)
	if err != nil {
		log.Fatal(fmt.Sprintf("New config object fail: %s", err.Error()))
	}
	_cfg.Init()
}

// 只有使用toml配置文件时，才支持环境变量注入
func InjectEnv(filename string) string {
	ext := filepath.Ext(filename)
	if ext != ".toml" {
		return filename
	}
	prefix := filename[:len(filename)-5]

	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return filename
	}

	injectEnvStr := injectEnv(string(data))
	err = ioutil.WriteFile(prefix+"_injectEnv"+ext, []byte(injectEnvStr), 0644)
	if err != nil {
		log.Error("injectenv persistence error, ", err)
	}

	filename = prefix + "_injectEnv" + ext

	log.Info("injectenv file, ", filename)

	return filename
}

// injectEnv 读取环境变量并注入
func injectEnv(src string) string {
	_str := strings.Replace(src, "\r", "", -1)
	lines := strings.Split(_str, "\n")

	envKeyPrefix := "PCV"
	prefix := ""
	for i, line := range lines {

		line = strings.Trim(line, " ")

		if len(line) == 0 || line[0] == '#' {
			continue
		}
		if line[0] == '[' && line[len(line)-1] == ']' {
			prefix = strings.TrimRight(strings.TrimLeft(line, "["), "]")
			// example: [table1.table2.table3], prefix=="table3"
			if dotLastIndex := strings.LastIndex(prefix, "."); dotLastIndex >= 0 && dotLastIndex < len(prefix)-1 {
				prefix = prefix[dotLastIndex+1:]
			}
			continue
		}
		key := strings.Trim(line[0:strings.Index(line, "=")], " ")
		envKey := strings.ToUpper(envKeyPrefix + "_" + prefix + "_" + key)
		envVal, exist := os.LookupEnv(envKey)
		if !exist {
			continue
		}
		val := strings.Trim(line[strings.Index(line, "=")+1:], " ")
		valSide := ""
		if val[0] == '"' {
			valSide = "\""
		}
		line = key + "=" + valSide + envVal + valSide
		log.Info("inject env: ", line)

		lines[i] = line

	}
	return strings.Join(lines, "\n")
}
