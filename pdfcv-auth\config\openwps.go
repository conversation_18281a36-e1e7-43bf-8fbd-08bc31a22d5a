package config

import (
	wpsApp "go-admin/openwps/internalapi/appinfo"
	wpsConfig "go-admin/openwps/internalapi/config"

	"github.com/pquerna/otp"
)

type AlgorithmStr string

const (
	AlgorithmSHA1   = "SHA1"
	AlgorithmSHA256 = "SHA256"
	AlgorithmSHA512 = "SHA512"
	AlgorithmMD5    = "MD5"
)

func (s AlgorithmStr) ToOtpAlgorithm() otp.Algorithm {
	switch s {
	case AlgorithmSHA1:
		return otp.AlgorithmSHA1
	case AlgorithmSHA256:
		return otp.AlgorithmSHA256
	case AlgorithmSHA512:
		return otp.AlgorithmSHA512
	case AlgorithmMD5:
		return otp.AlgorithmMD5
	default:
		panic("invalid otp algorithm")
	}
}

type Openwps struct {
	Appid       string
	Appkey      string
	Auth2Appid  string
	Auth2Appkey string
	Issuer      string
	Algorithm   AlgorithmStr
	Digits      otp.Digits
	Period      uint
	SecretSize  uint
	Skew        uint
}

func (e Openwps) Setup() {
	switch e.Digits {
	case otp.DigitsSix, otp.DigitsEight:
	default:
		panic("invalid otp digits")
	}

	switch e.Algorithm {
	case "SHA1", "SHA256", "SHA512", "MD5":
	default:
		panic("invalid otp algorithm")
	}

	wpsCfg, err := wpsConfig.NewConfig(e.Appid, e.Appkey, e.Auth2Appid, e.Auth2Appkey)
	if err != nil {
		panic(err)
	}
	wpsApp.Init(wpsCfg)
}

var OpenwpsConfig = new(Openwps)
