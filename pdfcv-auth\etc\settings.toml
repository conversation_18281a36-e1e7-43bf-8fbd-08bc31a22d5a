# 1. 只有使用toml配置文件时，才支持环境变量注入
# 2. 环境变量注入需要逐行扫描配置文件，因此只有存在于配置文件的配置项才会进行检测
# 替换规则示例:
# [table1.table2.table3]
# key1 = value1
# 如果配置环境变量 PCV_TABLE3_KEY1, 那么配置将会使用环境变量而非来自配置文件的值
[settings.application]
enabledp = false
host = "0.0.0.0"
mode = "prod"
name = "testApp"
port = 8000
readtimeout = 1
writertimeout = 2
[settings.logger]
enableddb = false
level = "trace"
path = "temp/logs"
stdout = ''
[settings.jwt]
secret = "go-admin"
timeout = 3600
[settings.db]
dbname = "dbname"
driver = "mysql"
host = "host"
password = "password"
port = 3306
user =""
source = "admin:12456@tcp(127.0.0.1:3306)/dbname?charset=utf8&parseTime=True&loc=Local&timeout=1000ms"

[settings.gen]
dbname = "dbname"
frontpath = "../pdfcv-auth-ui/src"
[settings.extend.demo]
name = "data"
[settings.cache]

memory = ""
[settings.queue.memory]
poolSize = 100

[settings.openwps]
appid = "AK20230202JWZHCN"
appkey = "b2ad50058dbfcfe07349e25487927420"
issuer = "pcv-test-auth.wps.cn"
algorithm= "SHA1"
digits= 6
period= 30
secretsize= 20
skew= 1
