# 1. 只有使用toml配置文件时，才支持环境变量注入
# 2. 环境变量注入需要逐行扫描配置文件，因此只有存在于配置文件的配置项才会进行检测
# 替换规则示例:
# [table1.table2.table3]
# key1 = value1
# 如果配置环境变量 PCV_TABLE3_KEY1, 那么配置将会使用环境变量而非来自配置文件的值
[settings.application]
enabledp = false
host = "0.0.0.0"
mode = "prod"
name = "testApp"
port = 8000
readtimeout = 1
writertimeout = 2
[settings.logger]
enableddb = false
level = "trace"
path = "temp/logs"
stdout = ''
[settings.jwt]
secret = "go-admin"
timeout = 3600
[settings.db]
dbname = "dbname"
driver = "mysql"
host = "host"
password = "password"
port = 3306
user =""
source = "admin:12456@tcp(127.0.0.1:3306)/dbname?charset=utf8&parseTime=True&loc=Local&timeout=1000ms"

[settings.gen]
dbname = "dbname"
frontpath = "../pdfcv-auth-ui/src"
[settings.extend.demo]
name = "data"
[settings.cache]

memory = ""
[settings.queue.memory]
poolSize = 100

[settings.wpsids]
# 统一身份认证
appid = "e7294019-6805-11ee-aab7-0abe8d1c9619"
appkey = "Q7jwo0KPnFc2eV3568zA1litB9TGUs4h"
# 动态令牌
auth2appid = "h46I8fo90Q1TzxPjqK73DHX2FtZJWw5B"
auth2appkey = "Fz3l0m6tcRb1STiwy8Qn9s2d7N54Vqhj"
issuer = "pcv-test-auth.wps.cn"
algorithm= "SHA1"
digits= 6
period= 30
secretsize= 20
skew= 1
