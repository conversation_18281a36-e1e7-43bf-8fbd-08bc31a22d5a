package appinfo

import (
	config2 "go-admin/openwps/internalapi/config"
)

const (
	defaultHost = "https://openapi.wps.cn"
)

var (
	config *config2.Config
	_debug bool = false
)

func Init(cfg *config2.Config) {
	config = cfg
}

func SetDebug(debug bool) {
	_debug = debug
}

func GetDebug() bool {
	return _debug
}
func isAvaliable() bool {
	if config == nil {
		panic("openapi not initialize yet.")
	}
	if len(config.AppId) == 0 || len(config.AppKey) == 0 {
		return false
	}
	return true
}
func GetAppid() string {
	if !isAvaliable() {

		return ""
	}
	return config.AppId
}

func GetAppKey() string {
	if !isAvaliable() {
		return ""
	}
	return config.AppKey
}

func GetOpenApiServer() string {
	if !isAvaliable() {
		return defaultHost
	}
	return config.OpenApiServer
}
