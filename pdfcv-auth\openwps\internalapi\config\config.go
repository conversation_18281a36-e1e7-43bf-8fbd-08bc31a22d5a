package config

import "fmt"

type Config struct {
	AppId, A<PERSON><PERSON><PERSON>, Auth2AppId, Auth2App<PERSON>ey, OpenApiServer string
}

const defaultServer = "https://portal.kwps.cn"

func NewConfig(appid, appkey, auth2_appid, auth2_appkey string) (*Config, error) {
	if len(appid) == 0 || len(appkey) == 0 || len(auth2_appid) == 0 || len(auth2_appkey) == 0 {
		return nil, fmt.Errorf("argument error, appid or appkey error")
	}
	return &Config{AppId: appid, AppKey: appkey, Auth2AppId: auth2_appid, Auth2AppKey: auth2_appkey, OpenApiServer: defaultServer}, nil
}
