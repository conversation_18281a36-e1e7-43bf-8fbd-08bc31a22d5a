package httputils

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"
	"io/ioutil"
	"log"
	"net/http"
	"net/url"
	"os"
	"regexp"
	"strings"
	"sync"

	"go-admin/openwps/internalapi/appinfo"
	"go-admin/openwps/internalapi/jsonutil"
	model "go-admin/openwps/model"
)

var (
	client *http.Client
	lock   sync.RWMutex
)

func init() {
	lock.Lock()
	defer lock.Unlock()

	client = &http.Client{}

}

func getClient() *http.Client {
	lock.RLock()
	defer lock.RUnlock()
	return client
}

func Init(proxy string) error {
	lock.Lock()
	defer lock.Unlock()

	if len(proxy) != 0 {
		proxyUrl, err := url.Parse(proxy)
		if err != nil {
			return err

		}
		client = &http.Client{Transport: &http.Transport{Proxy: http.ProxyURL(proxyUrl)}}

	} else {
		client = &http.Client{}

	}
	return nil
}

func get(url string, arg *url.Values, header map[string]string, result interface{}) error {
	if ret, _ := regexp.Match("\\?$", []byte(url)); !ret {
		url += "?"

	}
	url += arg.Encode()

	request, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return err

	}
	for k, v := range header {
		request.Header.Set(k, v)

	}
	return call(request, result)
}

func Get(url string, arg *url.Values, result interface{}) error {
	header := map[string]string{
		"content-type": "application/json;charset=utf-8",
	}
	return get(url, arg, header, result)
}

func Post(url string, arg map[string]interface{}, result interface{}) error {
	headers := map[string]string{
		"content-type": "application/json;charset=utf-8",
	}
	body, err := json.Marshal(arg)
	if err != nil {
		return err
	}

	return post(url, body, headers, result)
}

func post(sUrl string, body []byte, headers map[string]string, result interface{}) error {
	request, err := http.NewRequest("POST", sUrl, bytes.NewBuffer(body))
	if err != nil {
		return err

	}
	for k, v := range headers {
		request.Header.Set(k, v)

	}

	return call(request, result)
}

func Put(sUrl string, args map[string]interface{}, result interface{}) error {
	body, err := json.Marshal(args)
	if err != nil {
		return err
	}

	request, err := http.NewRequest("PUT", sUrl, bytes.NewBuffer(body))
	if err != nil {
		return err
	}
	request.Header.Set("content-type", "application/json;charset=utf-8")
	return call(request, result)
}

func Delete(sUrl string, args map[string]interface{}, result interface{}) error {
	body, err := json.Marshal(args)
	if err != nil {
		return err
	}

	request, err := http.NewRequest("DELETE", sUrl, bytes.NewBuffer(body))
	if err != nil {
		return err
	}
	request.Header.Set("content-type", "application/json;charset=utf-8")
	return call(request, result)
}

func UploadFile(sUrl string, file *os.File, header map[string]string, result interface{}) error {
	info, err := file.Stat()
	if err != nil {
		return err
	}

	request, err := http.NewRequest("PUT", sUrl, file)
	if err != nil {
		return err
	}

	request.ContentLength = info.Size()
	for k, v := range header {
		request.Header.Set(k, v)
	}

	if len(header["content-type"]) == 0 {
		request.Header.Set("content-type", "application/octet-stream")
	}
	return uploadcall(request, result)
}

func call(request *http.Request, result interface{}) error {
	resp, err := getClient().Do(request)
	if err != nil {
		return err

	}
	if resp.StatusCode != http.StatusOK {
		err := analysisNotOkMsg(resp.Body)
		return err
	}
	err = jsonutil.BodyToObject(resp.Body, result)
	defer resp.Body.Close()
	if err != nil {
		return err
	}
	return nil
}

func uploadcall(request *http.Request, result interface{}) error {
	resp, err := getClient().Do(request)
	if err != nil {
		return err
	}
	if resp.StatusCode != http.StatusOK {
		err := analysisNotOkMsg(resp.Body)
		return err
	}

	err = jsonutil.BodyToObject(resp.Body, result)
	defer resp.Body.Close()
	if err != nil {
		return err
	}

	var etag string = strings.ReplaceAll(resp.Header.Get("Etag"), "\"", "")
	result.(*model.UploadCommitData).Etag = etag

	return nil
}

func analysisNotOkMsg(body io.ReadCloser) error {
	defer body.Close()
	errMsg, err := ioutil.ReadAll(body)
	if err != nil {
		return err
	}

	if appinfo.GetDebug() {
		log.Println("respone body :" + string(errMsg))
	}

	if !json.Valid([]byte(errMsg)) {
		return errors.New(string(errMsg))
	}

	var rslt struct {
		Msg string `json:"msg"`
	}

	err = json.Unmarshal(errMsg, &rslt)
	if err != nil {
		return err
	}
	return errors.New(rslt.Msg)
}
