package jsonutil

import (
	"encoding/json"
	"io"
	"io/ioutil"
	"log"

	"go-admin/openwps/internalapi/appinfo"
)

func BodyToObject(body io.ReadCloser, result interface{}) error {
	bBody, err := ioutil.ReadAll(body)
	if appinfo.GetDebug() {
		log.Println("respone body :" + string(bBody))
	}

	if err != nil {
		return err
	}

	err = json.Unmarshal(bBody, result)
	if err != nil {
		return err
	}

	return nil
}
