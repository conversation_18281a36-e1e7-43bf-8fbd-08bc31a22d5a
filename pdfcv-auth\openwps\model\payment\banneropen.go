package model

type BannerOpen struct {
	BlueButton	  string	   `json:"blue_button"`
	BtnMsg	      string	   `json:"btn_msg"`
	BtnName	      string	   `json:"btn_name"`
	Extra	      string	   `json:"extra"`
	Icon	      string	   `json:"icon"`
	JumpMod	      string	   `json:"jump_mod"`
	LabelName     string	   `json:"label_name"`
	Link	      string	   `json:"link"`
	PanelLink	  string	   `json:"panel_link"`
	PriorityType  string	   `json:"priority_type"`
	Sceneid	      string	   `json:"sceneid"`
	Seceneid	  string	   `json:"seceneid"`
	ShowMod	      string	   `json:"show_mod"`
	Title	      string	   `json:"title"`
	Type	      string	   `json:"type"`
}
