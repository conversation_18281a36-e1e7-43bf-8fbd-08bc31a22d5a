package fileselect

import (
	"fmt"
	"net/url"
	"os"

	"go-admin/openwps/internalapi/appinfo"
	"go-admin/openwps/internalapi/httputils"
	commonmodel "go-admin/openwps/model"
	model "go-admin/openwps/model/fileselect"
)

//获取文件信息列表
func GetFileInfoList(accessToken, filCode string) (*model.FileInfoList,
	error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v2/selector/file/info"
	args := make(url.Values, 3)
	args.Set("appid", appinfo.GetAppid())
	args.Set("access_token", accessToken)
	args.Set("file_code", filCode)

	var filelist model.FileInfoList
	err := httputils.Get(sUrl, &args, &filelist)
	if err != nil {
		return nil, err
	}
	return &filelist, nil
}

//获取文件分享链接
func GetShareFileList(accessToken, filCode, ranges, permission, period string) (*model.ShareFileList,
	error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v2/selector/share/info"
	args := make(url.Values, 3)
	args.Set("appid", appinfo.GetAppid())
	args.Set("access_token", accessToken)
	args.Set("file_code", filCode)
	if len(ranges) == 0 {
		args.Set("ranges", ranges)
	}
	if len(permission) == 0 {
		args.Set("permission", permission)
	}
	if len(period) == 0 {
		args.Set("period", period)
	}

	var filelist model.ShareFileList
	err := httputils.Get(sUrl, &args, &filelist)
	if err != nil {
		return nil, err
	}
	return &filelist, nil
}

//获取文件下载链接
func GetDownloadFileList(accessToken, filCode string) (*model.DownloadFileList,
	error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v2/selector/download/url"
	args := make(url.Values, 3)
	args.Set("appid", appinfo.GetAppid())
	args.Set("access_token", accessToken)
	args.Set("file_code", filCode)

	var filelist model.DownloadFileList
	err := httputils.Get(sUrl, &args, &filelist)
	if err != nil {
		return nil, err
	}
	return &filelist, nil
}

//上传文件
func UploadFile(accessToken, filCode, filePath string, addNameIndex bool) (*model.UploadFileInfo, error) {
	file, err := os.Open(filePath)
	defer file.Close()
	if err != nil {
		return nil, err
	}

	info, err := file.Stat()
	if err != nil {
		return nil, err
	}
	uploadUrlData, err := getUploadUrl(accessToken, filCode, info.Name(), info.Size())
	if err != nil {
		return nil, err
	}

	commitData, err := Upload2CDN(uploadUrlData, file)
	if err != nil {
		return nil, err
	}
	return uploadFileInfo(accessToken, filCode, commitData.Fsha1, info.Name(), commitData.Etag, info.Size(), addNameIndex)
}

func getUploadUrl(accessToken, filCode, name string, size int64) (*model.UploadUrlData, error) {

	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v3/selector/upload/info"
	args := make(url.Values, 5)
	args.Set("appid", appinfo.GetAppid())
	args.Set("access_token", accessToken)
	args.Set("file_code", filCode)
	args.Set("size", fmt.Sprintf("%d", size))
	args.Set("name", name)

	var result struct {
		Result int                 `json:"result"`
		Data   model.UploadUrlData `json:"upload_info"`
	}

	err := httputils.Get(sUrl, &args, &result)
	if err != nil {
		return nil, err
	}

	return &result.Data, nil
}

func Upload2CDN(uploadURLData *model.UploadUrlData, file *os.File) (*commonmodel.UploadCommitData, error) {
	var commitData commonmodel.UploadCommitData
	err := httputils.UploadFile(uploadURLData.PutAuth.UploadUrl, file, uploadURLData.Headers, &commitData)
	if err != nil {
		return nil, err
	}

	return &commitData, nil
}

func uploadFileInfo(accessToken, filCode, sha1, name, etag string, size int64, addNameIndex bool) (*model.UploadFileInfo, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v3/selector/file/create"
	args := map[string]interface{}{
		"access_token":   accessToken,
		"appid":          appinfo.GetAppid(),
		"file_code":      filCode,
		"size":           size,
		"name":           name,
		"sha1":           sha1,
		"etag":           etag,
		"add_name_index": addNameIndex,
	}

	var result struct {
		Result int                  `json:"result"`
		Data   model.UploadFileInfo `json:"file_info"`
	}

	err := httputils.Post(sUrl, args, &result)

	if err != nil {
		return nil, err
	}

	return &result.Data, nil
}
