package oauth2

import (
	"errors"
	"net/url"
	"time"

	"go-admin/openwps/internalapi/appinfo"
	"go-admin/openwps/internalapi/httputils"
	model "go-admin/openwps/model/oauth2"
)

const (
	refreshExpires         int64  = 7776000
	refreshTokenOverdueMsg string = "refreshToken Overdue!"
)

type Oauth2 struct {
	token        *model.Token
	tokenExpires int64
}

func NewOauth2(code string) (*Oauth2, error) {
	sUrl := appinfo.GetOpenApiServer() + "/api/access/wpsopen/oauth2/token"
	args := make(url.Values, 3)
	args.Set("appid", appinfo.GetAppid())
	args.Set("appkey", appinfo.GetAppKey())
	args.Set("code", code)
	var result struct {
		Result int         `json:"result"`
		Token  model.Token `json:"token"`
		Msg    string      `json:"msg"`
	}
	err := httputils.Get(sUrl, &args, &result)
	if err != nil {
		return nil, err
	}
	if result.Result != 0 {
		return nil, errors.New("error: " + result.Msg)
	}

	self := new(Oauth2)
	curDate := time.Now().Unix()
	self.token = &result.Token
	self.tokenExpires = curDate + result.Token.ExpiresIn

	return self, nil
}

// 获取token(封装好刷新token的过程)
func (self *Oauth2) GetToken() (*model.Token, error) {
	curDate := time.Now().Unix()
	if curDate < self.tokenExpires {
		return self.token, nil
	}

	sUrl := appinfo.GetOpenApiServer() + "/api/access/wpsopen/oauth2/token/refresh"
	args := make(map[string]interface{}, 3)
	args["appid"] = appinfo.GetAppid()
	args["appkey"] = appinfo.GetAppKey()
	args["refresh_token"] = self.token.RefreshToken
	var result struct {
		Result int         `json:"result"`
		Token  model.Token `json:"token"`
		Msg    string      `json:"msg"`
	}
	err := httputils.Post(sUrl, args, &result)
	if err != nil {
		return nil, err
	}
	if result.Result != 0 {
		return nil, errors.New("error: " + result.Msg)
	}
	self.token = &result.Token
	self.tokenExpires = curDate + result.Token.ExpiresIn
	return self.token, nil
}

// 获取token
func GetToken(code string) (*model.Token, error) {
	sUrl := appinfo.GetOpenApiServer() + "/api/access/wpsopen/oauth2/token"
	args := make(url.Values, 3)
	args.Set("appid", appinfo.GetAppid())
	args.Set("appkey", appinfo.GetAppKey())
	args.Set("code", code)
	var result struct {
		Result int         `json:"result"`
		Token  model.Token `json:"token"`
		Msg    string      `json:"msg"`
	}
	err := httputils.Get(sUrl, &args, &result)
	if err != nil {
		return nil, err
	}
	if result.Result != 0 {
		return nil, errors.New("error: " + result.Msg)
	}
	return &result.Token, nil
}

// 刷新token
func RefreshToken(refreshToken string) (*model.Token, error) {
	sUrl := appinfo.GetOpenApiServer() + "/api/access/wpsopen/oauth2/token/refresh"
	args := make(map[string]interface{}, 3)
	args["appid"] = appinfo.GetAppid()
	args["appkey"] = appinfo.GetAppKey()
	args["refresh_token"] = refreshToken
	var result struct {
		Result int         `json:"result"`
		Token  model.Token `json:"token"`
		Msg    string      `json:"msg"`
	}
	err := httputils.Post(sUrl, args, &result)
	if err != nil {
		return nil, err
	}
	if result.Result != 0 {
		return nil, errors.New("error: " + result.Msg)
	}
	return &result.Token, nil
}

// 获取用户信息
func GetUserInfo(accessToken, openId string) (*model.UserInfo, error) {
	sUrl := appinfo.GetOpenApiServer() + "/api/access/wpsopen/oauth2/user"
	args := make(url.Values, 3)
	args.Set("appid", appinfo.GetAppid())
	args.Set("access_token", accessToken)
	// args.Set("openid", openId)
	var result struct {
		Result   int64          `json:"result"`
		UserInfo model.UserInfo `json:"user"`
		Msg      string         `json:"msg"`
	}

	err := httputils.Get(sUrl, &args, &result)
	if err != nil {
		return nil, err
	}

	if result.Result != 0 {
		return nil, errors.New(result.Msg)
	}
	return &result.UserInfo, nil
}

// 获取用户信息(建议用新版)
func GetUserInfoV3(accessToken string) (*model.UserInfo, error) {
	sUrl := appinfo.GetOpenApiServer() + "/api/access/oauth2/user"
	args := make(url.Values, 3)
	args.Set("access_token", accessToken)
	var result struct {
		Result   int64          `json:"result"`
		UserInfo model.UserInfo `json:"user"`
		Msg      string         `json:"msg"`
	}

	err := httputils.Get(sUrl, &args, &result.UserInfo)
	if err != nil {
		return nil, err
	}

	if result.Result != 0 {
		return nil, errors.New(result.Msg)
	}
	return &result.UserInfo, nil
}

// 获取 rpc_token
func GetRPCToken(accessToken, scope string) (string, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v2/rpc/token"
	args := map[string]interface{}{
		"access_token": accessToken,
		"scope":        scope,
		"appid":        appinfo.GetAppid(),
	}

	result := struct {
		Result   int64  `json:"result"`
		RPCToken string `json:"rpc_token"`
		Msg      string `json:"msg"`
	}{-1, "", ""}

	err := httputils.Post(sUrl, args, &result)
	if err != nil {
		return "", err
	}
	if result.Result != 0 {
		return "", errors.New(result.Msg)
	}
	return result.RPCToken, nil
}

// 校验 rpc_token
func CheckRPCToken(rpcToken, scope string) (bool, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v2/rpc/scope/authorize"
	args := map[string]interface{}{
		"rpc_token": rpcToken,
		"scope":     scope,
		"appid":     appinfo.GetAppid(),
	}

	result := struct {
		Result     int    `json:"result"`
		Authorized int    `json:"authorized"`
		Msg        string `json:"msg"`
	}{Result: 0, Authorized: -1}

	err := httputils.Post(sUrl, args, &result)
	if err != nil {
		return false, err
	}

	if result.Authorized == -1 {
		return false, errors.New("rpc_token verify fail: " + result.Msg)
	}
	return result.Authorized == 1, nil
}
