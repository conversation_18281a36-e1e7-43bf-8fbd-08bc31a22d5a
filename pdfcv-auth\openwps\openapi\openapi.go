package openapi

import (
	"errors"
	"sync"

	"go-admin/openwps/internalapi/appinfo"
	config2 "go-admin/openwps/internalapi/config"
	"go-admin/openwps/internalapi/httputils"
)

const defaultServer = "https://openapi.wps.cn"

var (
	Debug bool = false
)
var mu sync.Mutex

func Init(appid, appkey string) error {
	mu.Lock()
	defer mu.Unlock()

	if len(appid) == 0 || len(appkey) == 0 {
		return errors.New("argument error,cfg should not be nil or AppId, AppKey should must be not empty")
	}

	config := &config2.Config{AppId: appid, AppKey: appkey, OpenApiServer: defaultServer}
	appinfo.Init(config)
	//err := httputils.Init("http://127.0.0.1:8888")
	err := httputils.Init("")
	return err
}

func SetDebug(debug bool) {
	appinfo.SetDebug(debug)
}

func SetProxy(proxy string) error {
	return httputils.Init(proxy)
}
