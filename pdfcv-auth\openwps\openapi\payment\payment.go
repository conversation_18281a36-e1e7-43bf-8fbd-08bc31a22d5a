package payment

import (
	"errors"
	"net/url"

	"go-admin/openwps/internalapi/appinfo"
	"go-admin/openwps/internalapi/httputils"
	model "go-admin/openwps/model/payment"
)

type Payment int

const (
	QRCODE         Payment = iota //  二维码支付
	IOS                           //  ios支付 (预下单)
	ANDROID_WECHAT                //  安卓微信支付 (预下单)
	ANDROID_ALIPAY                //  安卓支付宝支付 (预下单)
)

func (p Payment) available() bool {
	if !(p >= 0 && p <= 3) {
		return false
	}
	return true
}

func (p Payment) ToString() string {
	switch p {
	case 0:
		return "qrcode"
	case 1:
		return "ios"
	case 2:
		return "android_wechat"
	case 3:
		return "android_alipay"
	default:
		return "other"
	}
}

// 判断用户是否有相关权益
func GetUsableService(accessToken, openid, serviceId string, totalNum int) (bool, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v2/vas/service/usable"
	body := map[string]interface{}{
		"access_token": accessToken,
		"openid":       openid,
		"service_id":   serviceId,
		"total_num":    totalNum,
		"appid":        appinfo.GetAppid(),
	}
	var result struct {
		Result int    `json:"result"`
		Msg    string `json:"msg"`
	}
	err := httputils.Post(sUrl, body, &result)
	if err != nil {
		return false, err
	}

	return result.Result == 0, nil
}

// 预下单接口
func PreorderPay(accessToken, openId, serviceId string, totalNum int, billno, subject, position, clientIp string) (string, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v2/vas/service/usable"
	body := map[string]interface{}{
		"access_token": accessToken,
		"openid":       openId,
		"service_id":   serviceId,
		"total_num":    totalNum,
		"billno":       billno,
		"subject":      subject,
		"position":     position,
		"client_ip":    clientIp,
		"appid":        appinfo.GetAppid(),
	}

	result := struct {
		Result int    `json:"result"`
		Msg    string `json:"msg"`
	}{Result: -1, Msg: "msg"}
	//err = jsonutil.BodyToObject(resp.Body, &result)
	err := httputils.Post(sUrl, body, &result)
	if err != nil {
		return "", err
	}

	if result.Result == 0 {
		return billno, nil
	}
	return "", nil
}

// 使用用户自身权益
func UseService(accessToken, openId, serviceId string, totalNum int64, billNo string) (bool, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v2/vas/service/use"
	body := map[string]interface{}{
		"access_token": accessToken,
		"openid":       openId,
		"service_id":   serviceId,
		"total_num":    totalNum,
		"billno":       billNo,
		"appid":        appinfo.GetAppid(),
	}

	result := struct {
		Result int    `json:"result"`
		Msg    string `json:"msg"`
	}{Result: -1, Msg: "msg"}
	//err = jsonutil.BodyToObject(resp.Body, &result)
	err := httputils.Post(sUrl, body, &result)
	if err != nil {
		return false, err
	}

	if result.Result == 0 {
		return true, nil
	}
	return false, errors.New(result.Msg)
}

// 零售下单
func CustomorderPay(accessToken, openId, serviceId string, billNo, subject, position string,
	payment Payment, totalFee, count int64) (*model.CustomPayInfo, error) {
	if !payment.available() {
		return nil, errors.New("argment error. wrong payment.")
	}

	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v2/vas/pay/customorder"
	body := map[string]interface{}{
		"access_token": accessToken,
		"openid":       openId,
		"service_id":   serviceId,
		"payment":      payment.ToString(),
		"total_fee":    totalFee,
		"count":        count,
		"billno":       billNo,
		"subject":      subject,
		"position":     position,
		"appid":        appinfo.GetAppid(),
	}

	result := struct {
		Result int                 `json:"result"`
		Msg    string              `json:"msg"`
		Data   model.CustomPayInfo `json:"data"`
	}{Result: -1, Msg: "msg"}

	err := httputils.Post(sUrl, body, &result)
	if err != nil {
		return nil, err
	}

	if result.Result == 0 {
		return &result.Data, nil
	}
	return nil, errors.New(result.Msg)
}

// 添加会员
func MemberAdd(accessToken, openId, orderId, phone string, memberId, days int64) (*model.MemberAdd, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v2/vas/pay/member/add"
	body := map[string]interface{}{
		"access_token": accessToken,
		"openid":       openId,
		"orderid":      orderId,
		"memberid":     memberId,
		"days":         days,
		"phone":        phone,
		"appid":        appinfo.GetAppid(),
	}

	result := struct {
		Result int             `json:"result"`
		Msg    string          `json:"msg"`
		Data   model.MemberAdd `json:"data"`
	}{Result: -1, Msg: "msg"}

	err := httputils.Post(sUrl, body, &result)
	if err != nil {
		return nil, err
	}

	if result.Result == 0 {
		return &result.Data, nil
	}
	return nil, errors.New(result.Msg)
}

// 开放标签
func BannerOpen(accessToken, mod, position string) (*model.BannerOpen, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v2/vas/banner/open"
	args := make(url.Values, 4)
	args.Set("appid", appinfo.GetAppid())
	args.Set("access_token", accessToken)
	args.Set("mod", mod)
	args.Set("position", position)

	result := struct {
		Result int              `json:"result"`
		Msg    string           `json:"msg"`
		Data   model.BannerOpen `json:"data"`
	}{Result: -1, Msg: "msg"}
	err := httputils.Get(sUrl, &args, &result)

	if err != nil {
		return nil, err
	}
	if result.Result == 0 {
		return &result.Data, nil
	}
	return nil, errors.New(result.Msg)
}
