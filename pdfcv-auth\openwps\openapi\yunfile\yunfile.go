package yunfile

import (
	"fmt"
	"net/url"
	"os"

	"go-admin/openwps/internalapi/appinfo"
	"go-admin/openwps/internalapi/httputils"
	commonmodel "go-admin/openwps/model"
	model "go-admin/openwps/model/yunfile"
	"go-admin/openwps/util/fileutil"
)

type FileOrder int

const (
	DESC FileOrder = iota // 逆序
	ASC                   // 正序
)

func (o FileOrder) ToString() string {
	switch o {
	case DESC:
		return "DESC"
	case ASC:
		return "ASC"
	default:
		return "DESC"
	}
}

type FileOrderByType int

const (
	FNAME FileOrderByType = iota // 依据文件名
	MTIME                        // 依据mtime
)

func (o FileOrderByType) ToString() string {
	switch o {
	case FNAME:
		return "fname"
	case MTIME:
		return "mtime"
	default:
		return "mtime"
	}
}

//  获取APP剩余空间
func GetRemainingSpace(accessToken string) (*model.RemainingSpace, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v2/appfile/remaining"
	args := make(url.Values, 2)
	args.Set("appid", appinfo.GetAppid())
	args.Set("access_token", accessToken)

	var remainingSpace model.RemainingSpace
	//err = jsonutil.BodyToObject(resp.Body, &remainingSpace)
	err := httputils.Get(sUrl, &args, &remainingSpace)
	if err != nil {
		return nil, err

	}
	return &remainingSpace, nil
}

//  获取文件列表
func GetAppFileList(accessToken string, parentId string, order FileOrder, orderBy FileOrderByType, offset, count int64) (*model.FileList, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v3/app/files/list"
	args := make(url.Values, 6)
	args.Set("appid", appinfo.GetAppid())
	args.Set("access_token", accessToken)
	args.Set("open_parentid", parentId)
	args.Set("order", order.ToString())
	args.Set("order_by", orderBy.ToString())

	if offset < 0 {
		args.Set("offset", "0")
	} else {
		args.Set("offset", fmt.Sprintf("%d", offset))

	}

	if count < 0 {
		args.Set("count", "0")
	} else {
		args.Set("count", fmt.Sprintf("%d", count))
	}

	var filelist model.FileList
	err := httputils.Get(sUrl, &args, &filelist)
	if err != nil {
		return nil, err
	}
	return &filelist, nil
}

// 创建文件夹
func CreateFolder(accessToken, name string, parentId string) (*model.CreateFolderInfo, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v3/app/folders/create"
	args := map[string]interface{}{
		"access_token":  accessToken,
		"appid":         appinfo.GetAppid(),
		"open_parentid": parentId,
		"name":          name,
	}
	//}
	var result struct {
		Result int                    `json:"result"`
		Data   model.CreateFolderInfo `json:"data"`
	}

	err := httputils.Post(sUrl, args, &result)
	if err != nil {
		return nil, err
	}

	return &result.Data, nil
}

// 上传文件
func UploadFile(accessToken, parentId, filePath string, addNameIndex bool) (*model.UploadFileInfo, error) {
	sha1, err := fileutil.GetFileSHA1(filePath)
	if err != nil {
		return nil, err
	}

	file, err := os.Open(filePath)
	defer file.Close()
	if err != nil {
		return nil, err
	}

	info, err := file.Stat()
	if err != nil {
		return nil, err
	}

	uploadData, err := getUploadUrl(accessToken, parentId, info.Name(), sha1, info.Size())
	if err != nil {
		return nil, err
	}

	commitData, err := Upload2CDN(uploadData, file)
	if err != nil {
		return nil, err
	}
	return UploadFileInfo(accessToken, sha1, parentId, info.Name(), info.Size(), commitData.Etag, addNameIndex)
}

// 更新文件
func UpdateFile(accessToken, parentId, fileId, filePath string) (*model.UploadFileInfo, error) {
	sha1, err := fileutil.GetFileSHA1(filePath)
	if err != nil {
		return nil, err
	}

	file, err := os.Open(filePath)
	defer file.Close()
	if err != nil {
		return nil, err
	}

	info, err := file.Stat()
	if err != nil {
		return nil, err
	}
	uploadData, err := getUploadUrl(accessToken, parentId, info.Name(), sha1, info.Size())
	if err != nil {
		return nil, err
	}

	commitData, err := Upload2CDN(uploadData, file)
	if err != nil {
		return nil, err
	}
	return UpdateFileInfo(accessToken, commitData.Fsha1, parentId, fileId, info.Name(), info.Size(), commitData.Etag)
}

// 获取分享文件链接
func GetFileLinkInfo(accessToken, fileId, permission, period string) (*model.LinkInfo, error) {

	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v3/app/files/link"
	args := make(url.Values, 3)
	args.Set("appid", appinfo.GetAppid())
	args.Set("access_token", accessToken)
	args.Set("open_fileid", fileId)
	if len(permission) == 0 {
		args.Set("permission", permission)
	}
	if len(period) == 0 {
		args.Set("period", period)
	}

	var result model.LinkInfo
	err := httputils.Get(sUrl, &args, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

// 获取文件下载链接
func GetFileDownloadUrl(accessToken string, fileId string) (*model.DownloadInfo, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v3/app/files/download/url"
	args := make(url.Values, 3)
	args.Set("appid", appinfo.GetAppid())
	args.Set("access_token", accessToken)
	args.Set("open_fileid", fileId)

	var result model.DownloadInfo
	//err = jsonutil.BodyToObject(resp.Body, &result)
	err := httputils.Get(sUrl, &args, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

// 文件重命名
func FileRename(accessToken, fileId, newName string) (*model.OptStatus, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v3/app/files/rename"
	args := map[string]interface{}{
		"access_token": accessToken,
		"open_fileid":  fileId,
		"new_name":     newName,
		"appid":        appinfo.GetAppid(),
	}

	var result model.OptStatus
	err := httputils.Put(sUrl, args, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// 文件复制, 文件复制，只能在本应用文件夹内进行
func FileCopyInApp(accessToken, fileIds string, fromParentId, toParentId string) (*model.OptStatus, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v3/app/files/copy"
	args := map[string]interface{}{
		"access_token":       accessToken,
		"appid":              appinfo.GetAppid(),
		"open_fileids":       fileIds,
		"open_from_parentid": fromParentId,
		"open_to_parentid":   toParentId,
	}

	var result model.OptStatus

	err := httputils.Post(sUrl, args, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// 文件移动, 文件移动，只能在本应用文件夹内进行
func FileMoveInApp(accessToken, fileIds string, fromParentId, toParentId string) (*model.OptStatus, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v3/app/files/move"
	args := map[string]interface{}{
		"access_token":       accessToken,
		"appid":              appinfo.GetAppid(),
		"open_fileids":       fileIds,
		"open_from_parentid": fromParentId,
		"open_to_parentid":   toParentId,
	}

	var result model.OptStatus
	err := httputils.Post(sUrl, args, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// 文件删除, 文件删除，只能在本应用文件夹内进行
func FileDelete(accessToken, fileIds string) (*model.OptStatus, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v3/app/files/delete"
	args := map[string]interface{}{
		"access_token": accessToken,
		"appid":        appinfo.GetAppid(),
		"open_fileids": fileIds,
	}

	var result model.OptStatus
	err := httputils.Delete(sUrl, args, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}

//  文件搜索(按文件名)
func SerchByName(accessToken, parentId, fName string, offset, count int64) (*model.FileList,
	error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v3/app/files/searchbyname"
	args := make(url.Values, 6)
	args.Set("appid", appinfo.GetAppid())
	args.Set("access_token", accessToken)
	args.Set("open_parentid", parentId)
	args.Set("file_name", fName)

	if offset < 0 {
		args.Set("offset", "0")
	} else {
		args.Set("offset", fmt.Sprintf("%d", offset))

	}

	if count < 0 {
		args.Set("count", "0")
	} else {
		args.Set("count", fmt.Sprintf("%d", count))
	}

	var filelist model.FileList
	err := httputils.Get(sUrl, &args, &filelist)
	if err != nil {
		return nil, err
	}
	return &filelist, nil
}

//  文件搜索(按内容)
func SerchByContent(accessToken, parentId, content string, offset, count int64) (*model.FileList,
	error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v3/app/files/searchbycontent"
	args := make(url.Values, 6)
	args.Set("appid", appinfo.GetAppid())
	args.Set("access_token", accessToken)
	args.Set("open_parentid", parentId)
	args.Set("content", content)

	if offset < 0 {
		args.Set("offset", "0")
	} else {
		args.Set("offset", fmt.Sprintf("%d", offset))

	}

	if count < 0 {
		args.Set("count", "0")
	} else {
		args.Set("count", fmt.Sprintf("%d", count))
	}

	var filelist model.FileList
	err := httputils.Get(sUrl, &args, &filelist)
	if err != nil {
		return nil, err
	}
	return &filelist, nil
}

// 新建文件
func CreateFile(accessToken, name, parentId string, addNameIndex bool) (*model.CreateFileInfo, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v3/app/files/create"
	args := map[string]interface{}{
		"access_token":   accessToken,
		"appid":          appinfo.GetAppid(),
		"parent_id":      parentId,
		"file_name":      name,
		"add_name_index": addNameIndex,
	}
	//}
	var result struct {
		Result int                  `json:"result"`
		File   model.CreateFileInfo `json:"file"`
	}

	err := httputils.Post(sUrl, args, &result)
	if err != nil {
		return nil, err
	}

	return &result.File, nil
}

// 获取文件信息
func GetFileInfo(accessToken string, fileId string) (*model.FileInfo, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v2/file/info"
	args := make(url.Values, 3)
	args.Set("appid", appinfo.GetAppid())
	args.Set("access_token", accessToken)
	args.Set("open_fileid", fileId)

	var result struct {
		Result int            `json:"result"`
		Data   model.FileInfo `json:"file_info"`
	}

	err := httputils.Get(sUrl, &args, &result)
	if err != nil {
		return nil, err
	}

	return &result.Data, nil
}

// 获取分享链接信息
func GetLinkInfo(accessToken string, linkUrl string) (*model.LinkInfo, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v2/file/link/info"
	args := make(url.Values, 3)
	args.Set("appid", appinfo.GetAppid())
	args.Set("access_token", accessToken)
	args.Set("link_url", linkUrl)

	var result struct {
		Result int            `json:"result"`
		Data   model.LinkInfo `json:"link_info"`
	}
	err := httputils.Get(sUrl, &args, &result)
	if err != nil {
		return nil, err
	}

	return &result.Data, nil
}

func getUploadUrl(accessToken, parentId, name, sha1 string, size int64) (*model.UploadUrlData, error) {

	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v4/app/files/upload/request"
	args := map[string]interface{}{
		"access_token":  accessToken,
		"appid":         appinfo.GetAppid(),
		"open_parentid": parentId,
		"size":          fmt.Sprintf("%d", size),
		"name":          name,
		"sha1":          sha1,
	}

	var result struct {
		Result int                 `json:"result"`
		Data   model.UploadUrlData `json:"data"`
	}
	err := httputils.Put(sUrl, args, &result)
	if err != nil {
		return nil, err
	}

	return &result.Data, nil
}

func Upload2CDN(uploadURLData *model.UploadUrlData, file *os.File) (*commonmodel.UploadCommitData, error) {
	var commitData commonmodel.UploadCommitData

	err := httputils.UploadFile(uploadURLData.UploadInfo.PutAuth.UploadUrl, file, uploadURLData.UploadInfo.Headers, &commitData)
	if err != nil {
		return nil, err
	}

	return &commitData, nil
}

func UploadFileInfo(accessToken, sha1, parentId, name string, size int64, etag string, addNameIndex bool) (*model.UploadFileInfo, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v3/app/files/upload/commit"
	args := map[string]interface{}{
		"access_token":   accessToken,
		"appid":          appinfo.GetAppid(),
		"size":           size,
		"open_parentid":  parentId,
		"name":           name,
		"sha1":           sha1,
		"etag":           etag,
		"add_name_index": addNameIndex,
	}
	var result struct {
		Result int                  `json:"result"`
		Data   model.UploadFileInfo `json:"data"`
	}

	err := httputils.Post(sUrl, args, &result)

	if err != nil {
		return nil, err
	}

	return &result.Data, nil
}

func UpdateFileInfo(accessToken, sha1, parentId, fileId, name string, size int64, etag string) (*model.UploadFileInfo, error) {
	sUrl := appinfo.GetOpenApiServer() + "/oauthapi/v3/app/files/upload/update"
	args := map[string]interface{}{
		"access_token":  accessToken,
		"appid":         appinfo.GetAppid(),
		"size":          size,
		"open_parentid": parentId,
		"open_fileid":   fileId,
		"name":          name,
		"sha1":          sha1,
		"etag":          etag,
	}
	var result struct {
		Result int                  `json:"result"`
		Data   model.UploadFileInfo `json:"data"`
	}

	err := httputils.Post(sUrl, args, &result)

	if err != nil {
		return nil, err
	}

	return &result.Data, nil
}
