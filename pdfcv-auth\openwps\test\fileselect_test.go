package test

import (
	"go-admin/openwps/openapi"
	"go-admin/openwps/openapi/fileselect"
	"testing"
)

var (
	selectAccessToken string = ""
	filecode          string = ""
)

func initSelectOpenapi() {
	openapi.Init("", "")
	openapi.SetDebug(true)
	//openapi.SetProxy("http://127.0.0.1:8888")
}

func TestGetFileInfoList(t *testing.T) {
	initSelectOpenapi()
	filelist, err := fileselect.GetFileInfoList(selectAccessToken, filecode)
	if err != nil {
		t.Errorf("%#v \n", err)
		t.FailNow()
	}

	t.Logf("%#v\n", filelist)
}

func TestGetShareFileList(t *testing.T) {
	initSelectOpenapi()
	filelist, err := fileselect.GetShareFileList(selectAccessToken, filecode, "anyone", "write", "0")
	if err != nil {
		t.<PERSON><PERSON><PERSON>("%#v \n", err)
		t.FailNow()
	}

	t.Logf("%#v\n", filelist)
}

func TestGetDownloadFileList(t *testing.T) {
	initSelectOpenapi()
	filelist, err := fileselect.GetDownloadFileList(selectAccessToken, filecode)
	if err != nil {
		t.Errorf("%#v \n", err)
		t.FailNow()
	}

	t.Logf("%#v\n", filelist)
}

func TestUploadFile(t *testing.T) {
	initSelectOpenapi()
	filepath := "C:\\Users\\<USER>\\Desktop\\test\\test.docx"
	fileinfo, err := fileselect.UploadFile(selectAccessToken, filecode, filepath, true)
	if err != nil {
		t.Logf("error: %#v \n", err)

		t.FailNow()
	}
	t.Logf("result: %#v \n", fileinfo)
}
