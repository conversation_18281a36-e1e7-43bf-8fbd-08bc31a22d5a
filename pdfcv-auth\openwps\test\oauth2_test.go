package test

import (
	"go-admin/openwps/openapi"
	"go-admin/openwps/openapi/oauth2"
	"testing"
)

func initDocOpenapi() {
	openapi.Init("", "")
	//openapi.SetProxy("http://127.0.0.1:8888")
	openapi.SetDebug(true)
}

var (
	oauth2AccessToken string = ""
	oauth2OpenId      string = ""
)

func TestGetTokenSelf(t *testing.T) {
	initDocOpenapi()
	oauth2, err := oauth2.NewOauth2("")
	result, err := oauth2.GetToken()

	if err != nil {
		t.Logf("err: %#v\n", err)
		t.FailNow()
	}
	t.Logf("result: %#v \n", result)

}

func TestGetToken(t *testing.T) {
	initDocOpenapi()
	result, err := oauth2.GetToken("")
	if err != nil {
		t.Logf("err: %#v\n", err)
		t.FailNow()
	}
	t.Logf("result: %#v \n", result)
}

func TestGetUserInfo(t *testing.T) {
	initDocOpenapi()
	result, err := oauth2.GetUserInfo(oauth2AccessToken, oauth2OpenId)
	if err != nil {
		t.Logf("err: %#v\n", err)
		t.FailNow()
	}
	t.Logf("result: %#v \n", result)
}

func TestGetUserInfov3(t *testing.T) {
	initDocOpenapi()
	result, err := oauth2.GetUserInfoV3(oauth2AccessToken)
	if err != nil {
		t.Logf("err: %#v\n", err)
		t.FailNow()
	}
	t.Logf("result: %#v \n", result)
}

func TestGetRPCToken(t *testing.T) {
	initDocOpenapi()
	// NOTICE: scope每次只能填写一项，否则生成的rpctoken校验将失败
	result, err := oauth2.GetRPCToken(oauth2AccessToken, "user_info")
	if err != nil {
		t.Logf("err: %#v\n", err)
		t.FailNow()
	}
	t.Logf("result: %#v \n", result)
}

func TestCheckRPCToken(t *testing.T) {
	initDocOpenapi()
	rpcToken, _ := oauth2.GetRPCToken(oauth2AccessToken, "user_info")

	result, err := oauth2.CheckRPCToken(rpcToken, "user_info")
	if err != nil {
		t.Logf("err: %#v\n", err)
		t.FailNow()
	}
	t.Logf("result: %#v \n", result)
}

func TestRefeshToken(t *testing.T) {
	initDocOpenapi()
	result, err := oauth2.RefreshToken("")
	if err != nil {
		t.Logf("err: %#v\n", err)
		t.FailNow()
	}
	t.Logf("result: %#v \n", result)

}
