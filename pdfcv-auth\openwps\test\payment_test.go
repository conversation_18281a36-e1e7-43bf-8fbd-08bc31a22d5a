package test

import (
	"go-admin/openwps/openapi"
	"go-admin/openwps/openapi/payment"
	"testing"
)

var (
	payAccessToken string = ""
	payOpenid      string = ""
	payserverId    string = ""
	//billNo		 string = ""
)

func initPayOpenapi() {
	openapi.Init("", "")
	//openapi.SetProxy("http://127.0.0.1:8888")
	openapi.SetDebug(true)
}
func TestGetUsableService(t *testing.T) {
	initPayOpenapi()
	result, err := payment.GetUsableService(payAccessToken, payOpenid, payserverId, 0)
	if err != nil {
		t.Logf("error: %#v \n", err)
		t.FailNow()
	}

	t.Logf("result: %#v \n", result)

}

func TestPreorderPay(t *testing.T) {
	initPayOpenapi()
	result, err := payment.PreorderPay(payAccessToken, payOpenid, payserverId, 0, "312414951", "100 year vip", "aaa",
		"*************")

	if err != nil {
		t.Logf("error: %#v \n", err)
		t.FailNow()
	}

	t.Logf("result: %#v \n", result)
}

func TestUseService(t *testing.T) {
	initPayOpenapi()
	result, err := payment.UseService(payAccessToken, payOpenid, payserverId, 1, "45134")
	if err != nil {
		t.Logf("%#v \n", err)
		t.FailNow()
	}

	t.Logf("%#v \n", result)
}

func TestCustomorderPay(t *testing.T) {
	initPayOpenapi()
	result, err := payment.CustomorderPay(payAccessToken, payOpenid, payserverId, "98dd90", "11", "458",
		payment.ANDROID_WECHAT, 78, 1)
	if err != nil {
		t.Logf("%#v \n", err)
		t.FailNow()
	}

	t.Logf("%#v \n", result)
}

func TestMemberAdd(t *testing.T) {
	initPayOpenapi()
	result, err := payment.MemberAdd(payAccessToken, payOpenid, "11111", "18292878114", 12, 31)
	if err != nil {
		t.Logf("%#v \n", err)
		t.FailNow()
	}

	t.Logf("%#v \n", result)
}

func TestBannerOpen(t *testing.T) {
	initPayOpenapi()
	result, err := payment.BannerOpen(payAccessToken, "scene_test_paper", "scene_test_paper_final_button")
	if err != nil {
		t.Logf("%#v \n", err)
		t.FailNow()
	}

	t.Logf("%#v \n", result)
}
