package test

import (
	"go-admin/openwps/openapi"
	"go-admin/openwps/openapi/yunfile"
	"testing"
)

var (
	fileAccessToken string = ""
)

func initFileOpenapi() {
	openapi.Init("", "")
	openapi.SetDebug(true)
	//openapi.SetProxy("http://127.0.0.1:8888")
}

func TestGetRemainingSpace(t *testing.T) {
	initFileOpenapi()
	remainSpace, err := yunfile.GetRemainingSpace(fileAccessToken)
	if err != nil {
		t.<PERSON>rrorf("%#v \n", err)
		t.FailNow()
	}

	t.Logf("%#v\n", remainSpace)
}

func TestGetAppFileList(t *testing.T) {
	initFileOpenapi()
	fileList, err := yunfile.GetAppFileList(fileAccessToken, "0", yunfile.ASC, yunfile.FNAME, 0, 12)
	if err != nil {
		t.Logf("error: %#v \n", err)
		t.FailNow()
	}
	t.Logf("result: %#v \n", fileList)
}

func TestCreateFolder(t *testing.T) {
	initFileOpenapi()
	folder, err := yunfile.CreateFolder(fileAccessToken, "test3", "0")
	if err != nil {
		t.Logf("error: %#v \n", err)
		t.FailNow()
	}
	t.Logf("result: %#v \n", folder)
}

func TestUploadFileSucess(t *testing.T) {
	initFileOpenapi()
	filePath := "C:\\Users\\<USER>\\Desktop\\test\\test.docx"
	fileInfo, err := yunfile.UploadFile(fileAccessToken, "0", filePath, true)
	if err != nil {
		t.Logf("error: %#v \n", err)

		t.FailNow()
	}
	t.Logf("result: %#v \n", fileInfo)
}

func TestUpdateFileSucess(t *testing.T) {
	initFileOpenapi()
	filePath := "C:\\Users\\<USER>\\Desktop\\test\\test.docx"
	fileInfo, err := yunfile.UpdateFile(fileAccessToken, "0", "opVxul_88WgwNW3QlpD4MLhA", filePath)
	if err != nil {
		t.Logf("error: %#v \n", err)

		t.FailNow()
	}
	t.Logf("result: %#v \n", fileInfo)
}

func TestGetFileLinkInfo(t *testing.T) {
	initFileOpenapi()
	result, err := yunfile.GetFileLinkInfo(fileAccessToken, "op5oAOvaQGHpW5jWaFPawwDQ", "write", "0")
	if err != nil {
		t.Errorf("%#v \n", err.Error())
		t.FailNow()
	}

	t.Logf("%#v\n", result)
}

func TestGetFileDownloadUrl(t *testing.T) {
	initFileOpenapi()
	result, err := yunfile.GetFileDownloadUrl(fileAccessToken, "op5oAOvaQGHpW5jWaFPawwDQ")
	if err != nil {
		t.Errorf("%#v \n", err)
		t.FailNow()
	}

	t.Logf("%#v\n", result)
}

func TestFileRename(t *testing.T) {
	initFileOpenapi()
	var fieldid string = "op5oAOvaQGHpW5jWaFPawwDQ"
	newName := "test11.doc"
	result, err := yunfile.FileRename(fileAccessToken, fieldid, newName)
	if err != nil {
		t.Errorf("%#v \n", err)
		t.FailNow()
	}
	t.Logf("%#v\n", result)
}

func TestFileCopyInApp(t *testing.T) {
	initFileOpenapi()
	// 修改为传入列表
	fieldids := "op5oAOvaQGHpW5jWaFPawwDQ"
	var fromFieldID string = "0"
	var toFieldID string = "opE_AKsJ_YQcRBhB134Ba21Q"

	result, err := yunfile.FileCopyInApp(fileAccessToken, fieldids, fromFieldID, toFieldID)
	if err != nil {
		t.Errorf("%#v \n", err)
		t.FailNow()
	}

	t.Logf("%#v\n", result)
}

func TestFileDelete(t *testing.T) {
	initFileOpenapi()
	fieldids := "op5oAOvaQGHpW5jWaFPawwDQ"
	result, err := yunfile.FileDelete(fileAccessToken, fieldids)
	if err != nil {
		t.Errorf("%#v \n", err)
		t.FailNow()
	}

	t.Logf("%#v\n", result)
}

func TestFileMoveInApp(t *testing.T) {
	initFileOpenapi()
	fieldids := "op5oAOvaQGHpW5jWaFPawwDQ"
	var fromFieldID string = "0"
	var toFieldID string = "opE_AKsJ_YQcRBhB134Ba21Q"
	result, err := yunfile.FileMoveInApp(fileAccessToken, fieldids, fromFieldID, toFieldID)
	if err != nil {
		t.Errorf("%#v \n", err)
		t.FailNow()
	}

	t.Logf("%#v\n", result)
}

func TestSerchByName(t *testing.T) {
	initFileOpenapi()
	var parentID string = "0"
	var fName string = "test.doc"
	fileList, err := yunfile.SerchByName(fileAccessToken, parentID, fName, 0, 100)
	if err != nil {
		t.Logf("error: %#v \n", err)
		t.FailNow()
	}
	t.Logf("result: %#v \n", fileList)
}

func TestFileByContent(t *testing.T) {
	initFileOpenapi()
	var parentID string = "0"
	var content string = "ff"
	fileList, err := yunfile.SerchByContent(fileAccessToken, parentID, content, 0, 100)
	if err != nil {
		t.Logf("error: %#v \n", err)
		t.FailNow()
	}
	t.Logf("result: %#v \n", fileList)
}

func TestCreateFile(t *testing.T) {
	initFileOpenapi()
	file, err := yunfile.CreateFile(fileAccessToken, "test3.pptx", "0", true)
	if err != nil {
		t.Logf("error: %#v \n", err)
		t.FailNow()
	}
	t.Logf("result: %#v \n", file)
}

func TestGetFileInfo(t *testing.T) {
	initFileOpenapi()
	result, err := yunfile.GetFileInfo(fileAccessToken, "opO1-1YVwhdfy5CEi8o32H8A")
	if err != nil {
		t.Errorf("%#v \n", err)
		t.FailNow()
	}

	t.Logf("%#v\n", result)
}

func TestGetLinkInfo(t *testing.T) {
	initFileOpenapi()
	result, err := yunfile.GetLinkInfo(fileAccessToken, "https://kdocs.cn/l/sO42QZB04")
	if err != nil {
		t.Errorf("%#v \n", err)
		t.FailNow()
	}

	t.Logf("%#v\n", result)
}
