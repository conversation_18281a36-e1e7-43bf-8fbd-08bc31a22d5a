package avatarutil

import (
	"net/url"
	"strings"
)

type Avatar struct {
	url *url.URL
}

func NewAvatar(urlStr string) (*Avatar, error) {
	urlObj, err := url.Parse(urlStr)
	if err != nil {
		return nil, err
	}
	return &Avatar{
		url: urlObj,
	}, nil
}

func (e *Avatar) GetAvatarUrl() string {
	return e.url.String()
}

func (e *Avatar) GetWpsId() string {
	id := e.url.Path
	if len(id) > 0 {
		id = id[1:]

		underline := strings.Index(id, "_")
		if underline != -1 {
			return id[:underline]
		}
		return id
	}
	return id
}
