package fileutil

import (
	"crypto/sha1"
	"encoding/hex"
	"io"
	"os"
)

func GetFileSHA1(filePath string) (string, error) {
	file, err := os.OpenFile(filePath, os.O_RDONLY, 004)
	if err != nil {
		return "", err
	}
	defer file.Close()

	buf := make([]byte, 4*1024)
	hash := sha1.New()

	for {
		n, err := file.Read(buf)
		if err != nil {
			if err == io.EOF {
				break
			} else {
				return "", nil
			}
		}
		hash.Write(buf[:n])
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}