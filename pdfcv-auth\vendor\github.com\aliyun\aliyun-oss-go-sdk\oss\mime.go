package oss

import (
	"mime"
	"path"
	"strings"
)

var extToMimeType = map[string]string{
	".xlsx":          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
	".xltx":          "application/vnd.openxmlformats-officedocument.spreadsheetml.template",
	".potx":          "application/vnd.openxmlformats-officedocument.presentationml.template",
	".ppsx":          "application/vnd.openxmlformats-officedocument.presentationml.slideshow",
	".pptx":          "application/vnd.openxmlformats-officedocument.presentationml.presentation",
	".sldx":          "application/vnd.openxmlformats-officedocument.presentationml.slide",
	".docx":          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
	".dotx":          "application/vnd.openxmlformats-officedocument.wordprocessingml.template",
	".xlam":          "application/vnd.ms-excel.addin.macroEnabled.12",
	".xlsb":          "application/vnd.ms-excel.sheet.binary.macroEnabled.12",
	".apk":           "application/vnd.android.package-archive",
	".hqx":           "application/mac-binhex40",
	".cpt":           "application/mac-compactpro",
	".doc":           "application/msword",
	".ogg":           "application/ogg",
	".pdf":           "application/pdf",
	".rtf":           "text/rtf",
	".mif":           "application/vnd.mif",
	".xls":           "application/vnd.ms-excel",
	".ppt":           "application/vnd.ms-powerpoint",
	".odc":           "application/vnd.oasis.opendocument.chart",
	".odb":           "application/vnd.oasis.opendocument.database",
	".odf":           "application/vnd.oasis.opendocument.formula",
	".odg":           "application/vnd.oasis.opendocument.graphics",
	".otg":           "application/vnd.oasis.opendocument.graphics-template",
	".odi":           "application/vnd.oasis.opendocument.image",
	".odp":           "application/vnd.oasis.opendocument.presentation",
	".otp":           "application/vnd.oasis.opendocument.presentation-template",
	".ods":           "application/vnd.oasis.opendocument.spreadsheet",
	".ots":           "application/vnd.oasis.opendocument.spreadsheet-template",
	".odt":           "application/vnd.oasis.opendocument.text",
	".odm":           "application/vnd.oasis.opendocument.text-master",
	".ott":           "application/vnd.oasis.opendocument.text-template",
	".oth":           "application/vnd.oasis.opendocument.text-web",
	".sxw":           "application/vnd.sun.xml.writer",
	".stw":           "application/vnd.sun.xml.writer.template",
	".sxc":           "application/vnd.sun.xml.calc",
	".stc":           "application/vnd.sun.xml.calc.template",
	".sxd":           "application/vnd.sun.xml.draw",
	".std":           "application/vnd.sun.xml.draw.template",
	".sxi":           "application/vnd.sun.xml.impress",
	".sti":           "application/vnd.sun.xml.impress.template",
	".sxg":           "application/vnd.sun.xml.writer.global",
	".sxm":           "application/vnd.sun.xml.math",
	".sis":           "application/vnd.symbian.install",
	".wbxml":         "application/vnd.wap.wbxml",
	".wmlc":          "application/vnd.wap.wmlc",
	".wmlsc":         "application/vnd.wap.wmlscriptc",
	".bcpio":         "application/x-bcpio",
	".torrent":       "application/x-bittorrent",
	".bz2":           "application/x-bzip2",
	".vcd":           "application/x-cdlink",
	".pgn":           "application/x-chess-pgn",
	".cpio":          "application/x-cpio",
	".csh":           "application/x-csh",
	".dvi":           "application/x-dvi",
	".spl":           "application/x-futuresplash",
	".gtar":          "application/x-gtar",
	".hdf":           "application/x-hdf",
	".jar":           "application/x-java-archive",
	".jnlp":          "application/x-java-jnlp-file",
	".js":            "application/x-javascript",
	".ksp":           "application/x-kspread",
	".chrt":          "application/x-kchart",
	".kil":           "application/x-killustrator",
	".latex":         "application/x-latex",
	".rpm":           "application/x-rpm",
	".sh":            "application/x-sh",
	".shar":          "application/x-shar",
	".swf":           "application/x-shockwave-flash",
	".sit":           "application/x-stuffit",
	".sv4cpio":       "application/x-sv4cpio",
	".sv4crc":        "application/x-sv4crc",
	".tar":           "application/x-tar",
	".tcl":           "application/x-tcl",
	".tex":           "application/x-tex",
	".man":           "application/x-troff-man",
	".me":            "application/x-troff-me",
	".ms":            "application/x-troff-ms",
	".ustar":         "application/x-ustar",
	".src":           "application/x-wais-source",
	".zip":           "application/zip",
	".m3u":           "audio/x-mpegurl",
	".ra":            "audio/x-pn-realaudio",
	".wav":           "audio/x-wav",
	".wma":           "audio/x-ms-wma",
	".wax":           "audio/x-ms-wax",
	".pdb":           "chemical/x-pdb",
	".xyz":           "chemical/x-xyz",
	".bmp":           "image/bmp",
	".gif":           "image/gif",
	".ief":           "image/ief",
	".png":           "image/png",
	".wbmp":          "image/vnd.wap.wbmp",
	".ras":           "image/x-cmu-raster",
	".pnm":           "image/x-portable-anymap",
	".pbm":           "image/x-portable-bitmap",
	".pgm":           "image/x-portable-graymap",
	".ppm":           "image/x-portable-pixmap",
	".rgb":           "image/x-rgb",
	".xbm":           "image/x-xbitmap",
	".xpm":           "image/x-xpixmap",
	".xwd":           "image/x-xwindowdump",
	".css":           "text/css",
	".rtx":           "text/richtext",
	".tsv":           "text/tab-separated-values",
	".jad":           "text/vnd.sun.j2me.app-descriptor",
	".wml":           "text/vnd.wap.wml",
	".wmls":          "text/vnd.wap.wmlscript",
	".etx":           "text/x-setext",
	".mxu":           "video/vnd.mpegurl",
	".flv":           "video/x-flv",
	".wm":            "video/x-ms-wm",
	".wmv":           "video/x-ms-wmv",
	".wmx":           "video/x-ms-wmx",
	".wvx":           "video/x-ms-wvx",
	".avi":           "video/x-msvideo",
	".movie":         "video/x-sgi-movie",
	".ice":           "x-conference/x-cooltalk",
	".3gp":           "video/3gpp",
	".ai":            "application/postscript",
	".aif":           "audio/x-aiff",
	".aifc":          "audio/x-aiff",
	".aiff":          "audio/x-aiff",
	".asc":           "text/plain",
	".atom":          "application/atom+xml",
	".au":            "audio/basic",
	".bin":           "application/octet-stream",
	".cdf":           "application/x-netcdf",
	".cgm":           "image/cgm",
	".class":         "application/octet-stream",
	".dcr":           "application/x-director",
	".dif":           "video/x-dv",
	".dir":           "application/x-director",
	".djv":           "image/vnd.djvu",
	".djvu":          "image/vnd.djvu",
	".dll":           "application/octet-stream",
	".dmg":           "application/octet-stream",
	".dms":           "application/octet-stream",
	".dtd":           "application/xml-dtd",
	".dv":            "video/x-dv",
	".dxr":           "application/x-director",
	".eps":           "application/postscript",
	".exe":           "application/octet-stream",
	".ez":            "application/andrew-inset",
	".gram":          "application/srgs",
	".grxml":         "application/srgs+xml",
	".gz":            "application/x-gzip",
	".htm":           "text/html",
	".html":          "text/html",
	".ico":           "image/x-icon",
	".ics":           "text/calendar",
	".ifb":           "text/calendar",
	".iges":          "model/iges",
	".igs":           "model/iges",
	".jp2":           "image/jp2",
	".jpe":           "image/jpeg",
	".jpeg":          "image/jpeg",
	".jpg":           "image/jpeg",
	".kar":           "audio/midi",
	".lha":           "application/octet-stream",
	".lzh":           "application/octet-stream",
	".m4a":           "audio/mp4a-latm",
	".m4p":           "audio/mp4a-latm",
	".m4u":           "video/vnd.mpegurl",
	".m4v":           "video/x-m4v",
	".mac":           "image/x-macpaint",
	".mathml":        "application/mathml+xml",
	".mesh":          "model/mesh",
	".mid":           "audio/midi",
	".midi":          "audio/midi",
	".mov":           "video/quicktime",
	".mp2":           "audio/mpeg",
	".mp3":           "audio/mpeg",
	".mp4":           "video/mp4",
	".mpe":           "video/mpeg",
	".mpeg":          "video/mpeg",
	".mpg":           "video/mpeg",
	".mpga":          "audio/mpeg",
	".msh":           "model/mesh",
	".nc":            "application/x-netcdf",
	".oda":           "application/oda",
	".ogv":           "video/ogv",
	".pct":           "image/pict",
	".pic":           "image/pict",
	".pict":          "image/pict",
	".pnt":           "image/x-macpaint",
	".pntg":          "image/x-macpaint",
	".ps":            "application/postscript",
	".qt":            "video/quicktime",
	".qti":           "image/x-quicktime",
	".qtif":          "image/x-quicktime",
	".ram":           "audio/x-pn-realaudio",
	".rdf":           "application/rdf+xml",
	".rm":            "application/vnd.rn-realmedia",
	".roff":          "application/x-troff",
	".sgm":           "text/sgml",
	".sgml":          "text/sgml",
	".silo":          "model/mesh",
	".skd":           "application/x-koan",
	".skm":           "application/x-koan",
	".skp":           "application/x-koan",
	".skt":           "application/x-koan",
	".smi":           "application/smil",
	".smil":          "application/smil",
	".snd":           "audio/basic",
	".so":            "application/octet-stream",
	".svg":           "image/svg+xml",
	".t":             "application/x-troff",
	".texi":          "application/x-texinfo",
	".texinfo":       "application/x-texinfo",
	".tif":           "image/tiff",
	".tiff":          "image/tiff",
	".tr":            "application/x-troff",
	".txt":           "text/plain",
	".vrml":          "model/vrml",
	".vxml":          "application/voicexml+xml",
	".webm":          "video/webm",
	".wrl":           "model/vrml",
	".xht":           "application/xhtml+xml",
	".xhtml":         "application/xhtml+xml",
	".xml":           "application/xml",
	".xsl":           "application/xml",
	".xslt":          "application/xslt+xml",
	".xul":           "application/vnd.mozilla.xul+xml",
	".webp":          "image/webp",
	".323":           "text/h323",
	".aab":           "application/x-authoware-bin",
	".aam":           "application/x-authoware-map",
	".aas":           "application/x-authoware-seg",
	".acx":           "application/internet-property-stream",
	".als":           "audio/X-Alpha5",
	".amc":           "application/x-mpeg",
	".ani":           "application/octet-stream",
	".asd":           "application/astound",
	".asf":           "video/x-ms-asf",
	".asn":           "application/astound",
	".asp":           "application/x-asap",
	".asr":           "video/x-ms-asf",
	".asx":           "video/x-ms-asf",
	".avb":           "application/octet-stream",
	".awb":           "audio/amr-wb",
	".axs":           "application/olescript",
	".bas":           "text/plain",
	".bin ":          "application/octet-stream",
	".bld":           "application/bld",
	".bld2":          "application/bld2",
	".bpk":           "application/octet-stream",
	".c":             "text/plain",
	".cal":           "image/x-cals",
	".cat":           "application/vnd.ms-pkiseccat",
	".ccn":           "application/x-cnc",
	".cco":           "application/x-cocoa",
	".cer":           "application/x-x509-ca-cert",
	".cgi":           "magnus-internal/cgi",
	".chat":          "application/x-chat",
	".clp":           "application/x-msclip",
	".cmx":           "image/x-cmx",
	".co":            "application/x-cult3d-object",
	".cod":           "image/cis-cod",
	".conf":          "text/plain",
	".cpp":           "text/plain",
	".crd":           "application/x-mscardfile",
	".crl":           "application/pkix-crl",
	".crt":           "application/x-x509-ca-cert",
	".csm":           "chemical/x-csml",
	".csml":          "chemical/x-csml",
	".cur":           "application/octet-stream",
	".dcm":           "x-lml/x-evm",
	".dcx":           "image/x-dcx",
	".der":           "application/x-x509-ca-cert",
	".dhtml":         "text/html",
	".dot":           "application/msword",
	".dwf":           "drawing/x-dwf",
	".dwg":           "application/x-autocad",
	".dxf":           "application/x-autocad",
	".ebk":           "application/x-expandedbook",
	".emb":           "chemical/x-embl-dl-nucleotide",
	".embl":          "chemical/x-embl-dl-nucleotide",
	".epub":          "application/epub+zip",
	".eri":           "image/x-eri",
	".es":            "audio/echospeech",
	".esl":           "audio/echospeech",
	".etc":           "application/x-earthtime",
	".evm":           "x-lml/x-evm",
	".evy":           "application/envoy",
	".fh4":           "image/x-freehand",
	".fh5":           "image/x-freehand",
	".fhc":           "image/x-freehand",
	".fif":           "application/fractals",
	".flr":           "x-world/x-vrml",
	".fm":            "application/x-maker",
	".fpx":           "image/x-fpx",
	".fvi":           "video/isivideo",
	".gau":           "chemical/x-gaussian-input",
	".gca":           "application/x-gca-compressed",
	".gdb":           "x-lml/x-gdb",
	".gps":           "application/x-gps",
	".h":             "text/plain",
	".hdm":           "text/x-hdml",
	".hdml":          "text/x-hdml",
	".hlp":           "application/winhlp",
	".hta":           "application/hta",
	".htc":           "text/x-component",
	".hts":           "text/html",
	".htt":           "text/webviewhtml",
	".ifm":           "image/gif",
	".ifs":           "image/ifs",
	".iii":           "application/x-iphone",
	".imy":           "audio/melody",
	".ins":           "application/x-internet-signup",
	".ips":           "application/x-ipscript",
	".ipx":           "application/x-ipix",
	".isp":           "application/x-internet-signup",
	".it":            "audio/x-mod",
	".itz":           "audio/x-mod",
	".ivr":           "i-world/i-vrml",
	".j2k":           "image/j2k",
	".jam":           "application/x-jam",
	".java":          "text/plain",
	".jfif":          "image/pipeg",
	".jpz":           "image/jpeg",
	".jwc":           "application/jwc",
	".kjx":           "application/x-kjx",
	".lak":           "x-lml/x-lak",
	".lcc":           "application/fastman",
	".lcl":           "application/x-digitalloca",
	".lcr":           "application/x-digitalloca",
	".lgh":           "application/lgh",
	".lml":           "x-lml/x-lml",
	".lmlpack":       "x-lml/x-lmlpack",
	".log":           "text/plain",
	".lsf":           "video/x-la-asf",
	".lsx":           "video/x-la-asf",
	".m13":           "application/x-msmediaview",
	".m14":           "application/x-msmediaview",
	".m15":           "audio/x-mod",
	".m3url":         "audio/x-mpegurl",
	".m4b":           "audio/mp4a-latm",
	".ma1":           "audio/ma1",
	".ma2":           "audio/ma2",
	".ma3":           "audio/ma3",
	".ma5":           "audio/ma5",
	".map":           "magnus-internal/imagemap",
	".mbd":           "application/mbedlet",
	".mct":           "application/x-mascot",
	".mdb":           "application/x-msaccess",
	".mdz":           "audio/x-mod",
	".mel":           "text/x-vmel",
	".mht":           "message/rfc822",
	".mhtml":         "message/rfc822",
	".mi":            "application/x-mif",
	".mil":           "image/x-cals",
	".mio":           "audio/x-mio",
	".mmf":           "application/x-skt-lbs",
	".mng":           "video/x-mng",
	".mny":           "application/x-msmoney",
	".moc":           "application/x-mocha",
	".mocha":         "application/x-mocha",
	".mod":           "audio/x-mod",
	".mof":           "application/x-yumekara",
	".mol":           "chemical/x-mdl-molfile",
	".mop":           "chemical/x-mopac-input",
	".mpa":           "video/mpeg",
	".mpc":           "application/vnd.mpohun.certificate",
	".mpg4":          "video/mp4",
	".mpn":           "application/vnd.mophun.application",
	".mpp":           "application/vnd.ms-project",
	".mps":           "application/x-mapserver",
	".mpv2":          "video/mpeg",
	".mrl":           "text/x-mrml",
	".mrm":           "application/x-mrm",
	".msg":           "application/vnd.ms-outlook",
	".mts":           "application/metastream",
	".mtx":           "application/metastream",
	".mtz":           "application/metastream",
	".mvb":           "application/x-msmediaview",
	".mzv":           "application/metastream",
	".nar":           "application/zip",
	".nbmp":          "image/nbmp",
	".ndb":           "x-lml/x-ndb",
	".ndwn":          "application/ndwn",
	".nif":           "application/x-nif",
	".nmz":           "application/x-scream",
	".nokia-op-logo": "image/vnd.nok-oplogo-color",
	".npx":           "application/x-netfpx",
	".nsnd":          "audio/nsnd",
	".nva":           "application/x-neva1",
	".nws":           "message/rfc822",
	".oom":           "application/x-AtlasMate-Plugin",
	".p10":           "application/pkcs10",
	".p12":           "application/x-pkcs12",
	".p7b":           "application/x-pkcs7-certificates",
	".p7c":           "application/x-pkcs7-mime",
	".p7m":           "application/x-pkcs7-mime",
	".p7r":           "application/x-pkcs7-certreqresp",
	".p7s":           "application/x-pkcs7-signature",
	".pac":           "audio/x-pac",
	".pae":           "audio/x-epac",
	".pan":           "application/x-pan",
	".pcx":           "image/x-pcx",
	".pda":           "image/x-pda",
	".pfr":           "application/font-tdpfr",
	".pfx":           "application/x-pkcs12",
	".pko":           "application/ynd.ms-pkipko",
	".pm":            "application/x-perl",
	".pma":           "application/x-perfmon",
	".pmc":           "application/x-perfmon",
	".pmd":           "application/x-pmd",
	".pml":           "application/x-perfmon",
	".pmr":           "application/x-perfmon",
	".pmw":           "application/x-perfmon",
	".pnz":           "image/png",
	".pot,":          "application/vnd.ms-powerpoint",
	".pps":           "application/vnd.ms-powerpoint",
	".pqf":           "application/x-cprplayer",
	".pqi":           "application/cprplayer",
	".prc":           "application/x-prc",
	".prf":           "application/pics-rules",
	".prop":          "text/plain",
	".proxy":         "application/x-ns-proxy-autoconfig",
	".ptlk":          "application/listenup",
	".pub":           "application/x-mspublisher",
	".pvx":           "video/x-pv-pvx",
	".qcp":           "audio/vnd.qcelp",
	".r3t":           "text/vnd.rn-realtext3d",
	".rar":           "application/octet-stream",
	".rc":            "text/plain",
	".rf":            "image/vnd.rn-realflash",
	".rlf":           "application/x-richlink",
	".rmf":           "audio/x-rmf",
	".rmi":           "audio/mid",
	".rmm":           "audio/x-pn-realaudio",
	".rmvb":          "audio/x-pn-realaudio",
	".rnx":           "application/vnd.rn-realplayer",
	".rp":            "image/vnd.rn-realpix",
	".rt":            "text/vnd.rn-realtext",
	".rte":           "x-lml/x-gps",
	".rtg":           "application/metastream",
	".rv":            "video/vnd.rn-realvideo",
	".rwc":           "application/x-rogerwilco",
	".s3m":           "audio/x-mod",
	".s3z":           "audio/x-mod",
	".sca":           "application/x-supercard",
	".scd":           "application/x-msschedule",
	".sct":           "text/scriptlet",
	".sdf":           "application/e-score",
	".sea":           "application/x-stuffit",
	".setpay":        "application/set-payment-initiation",
	".setreg":        "application/set-registration-initiation",
	".shtml":         "text/html",
	".shtm":          "text/html",
	".shw":           "application/presentations",
	".si6":           "image/si6",
	".si7":           "image/vnd.stiwap.sis",
	".si9":           "image/vnd.lgtwap.sis",
	".slc":           "application/x-salsa",
	".smd":           "audio/x-smd",
	".smp":           "application/studiom",
	".smz":           "audio/x-smd",
	".spc":           "application/x-pkcs7-certificates",
	".spr":           "application/x-sprite",
	".sprite":        "application/x-sprite",
	".sdp":           "application/sdp",
	".spt":           "application/x-spt",
	".sst":           "application/vnd.ms-pkicertstore",
	".stk":           "application/hyperstudio",
	".stl":           "application/vnd.ms-pkistl",
	".stm":           "text/html",
	".svf":           "image/vnd",
	".svh":           "image/svh",
	".svr":           "x-world/x-svr",
	".swfl":          "application/x-shockwave-flash",
	".tad":           "application/octet-stream",
	".talk":          "text/x-speech",
	".taz":           "application/x-tar",
	".tbp":           "application/x-timbuktu",
	".tbt":           "application/x-timbuktu",
	".tgz":           "application/x-compressed",
	".thm":           "application/vnd.eri.thm",
	".tki":           "application/x-tkined",
	".tkined":        "application/x-tkined",
	".toc":           "application/toc",
	".toy":           "image/toy",
	".trk":           "x-lml/x-gps",
	".trm":           "application/x-msterminal",
	".tsi":           "audio/tsplayer",
	".tsp":           "application/dsptype",
	".ttf":           "application/octet-stream",
	".ttz":           "application/t-time",
	".uls":           "text/iuls",
	".ult":           "audio/x-mod",
	".uu":            "application/x-uuencode",
	".uue":           "application/x-uuencode",
	".vcf":           "text/x-vcard",
	".vdo":           "video/vdo",
	".vib":           "audio/vib",
	".viv":           "video/vivo",
	".vivo":          "video/vivo",
	".vmd":           "application/vocaltec-media-desc",
	".vmf":           "application/vocaltec-media-file",
	".vmi":           "application/x-dreamcast-vms-info",
	".vms":           "application/x-dreamcast-vms",
	".vox":           "audio/voxware",
	".vqe":           "audio/x-twinvq-plugin",
	".vqf":           "audio/x-twinvq",
	".vql":           "audio/x-twinvq",
	".vre":           "x-world/x-vream",
	".vrt":           "x-world/x-vrt",
	".vrw":           "x-world/x-vream",
	".vts":           "workbook/formulaone",
	".wcm":           "application/vnd.ms-works",
	".wdb":           "application/vnd.ms-works",
	".web":           "application/vnd.xara",
	".wi":            "image/wavelet",
	".wis":           "application/x-InstallShield",
	".wks":           "application/vnd.ms-works",
	".wmd":           "application/x-ms-wmd",
	".wmf":           "application/x-msmetafile",
	".wmlscript":     "text/vnd.wap.wmlscript",
	".wmz":           "application/x-ms-wmz",
	".wpng":          "image/x-up-wpng",
	".wps":           "application/vnd.ms-works",
	".wpt":           "x-lml/x-gps",
	".wri":           "application/x-mswrite",
	".wrz":           "x-world/x-vrml",
	".ws":            "text/vnd.wap.wmlscript",
	".wsc":           "application/vnd.wap.wmlscriptc",
	".wv":            "video/wavelet",
	".wxl":           "application/x-wxl",
	".x-gzip":        "application/x-gzip",
	".xaf":           "x-world/x-vrml",
	".xar":           "application/vnd.xara",
	".xdm":           "application/x-xdma",
	".xdma":          "application/x-xdma",
	".xdw":           "application/vnd.fujixerox.docuworks",
	".xhtm":          "application/xhtml+xml",
	".xla":           "application/vnd.ms-excel",
	".xlc":           "application/vnd.ms-excel",
	".xll":           "application/x-excel",
	".xlm":           "application/vnd.ms-excel",
	".xlt":           "application/vnd.ms-excel",
	".xlw":           "application/vnd.ms-excel",
	".xm":            "audio/x-mod",
	".xmz":           "audio/x-mod",
	".xof":           "x-world/x-vrml",
	".xpi":           "application/x-xpinstall",
	".xsit":          "text/xml",
	".yz1":           "application/x-yz1",
	".z":             "application/x-compress",
	".zac":           "application/x-zaurus-zac",
	".json":          "application/json",
}

// TypeByExtension returns the MIME type associated with the file extension ext.
// gets the file's MIME type for HTTP header Content-Type
func TypeByExtension(filePath string) string {
	typ := mime.TypeByExtension(path.Ext(filePath))
	if typ == "" {
		typ = extToMimeType[strings.ToLower(path.Ext(filePath))]
	} else {
		if strings.HasPrefix(typ, "text/") && strings.Contains(typ, "charset=") {
			typ = removeCharsetInMimeType(typ)
		}
	}
	return typ
}

// Remove charset from mime type
func removeCharsetInMimeType(typ string) (str string) {
	temArr := strings.Split(typ, ";")
	var builder strings.Builder
	for i, s := range temArr {
		tmpStr := strings.Trim(s, " ")
		if strings.Contains(tmpStr, "charset=") {
			continue
		}
		if i == 0 {
			builder.WriteString(s)
		} else {
			builder.WriteString("; " + s)
		}
	}
	return builder.String()
}
