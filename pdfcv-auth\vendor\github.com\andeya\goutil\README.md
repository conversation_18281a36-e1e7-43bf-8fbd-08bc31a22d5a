# goutil [![Build Status](https://travis-ci.org/andeya/goutil.svg?branch=mgoutil)](https://travis-ci.org/andeya/goutil) [![report card](https://goreportcard.com/badge/github.com/andeya/goutil?style=flat-square)](http://goreportcard.com/report/andeya/goutil) [![GoDoc](https://img.shields.io/badge/godoc-reference-blue.svg?style=flat-square)](http://godoc.org/github.com/andeya/goutil)

Golang common tool functions or components.

## Inclusion criteria

- Only rely on the Go standard package
- Functions or lightweight packages
- Non-business related general tools
