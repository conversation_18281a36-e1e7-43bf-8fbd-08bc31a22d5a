// Code generated by go generate gen.go; DO NOT EDIT.

//go:generate go run gen.go

package atom

const (
	A                         Atom = 0x1
	Abbr                      Atom = 0x4
	Accept                    Atom = 0x1a06
	AcceptCharset             Atom = 0x1a0e
	Accesskey                 Atom = 0x2c09
	Acronym                   Atom = 0xaa07
	Action                    Atom = 0x27206
	Address                   Atom = 0x6f307
	Align                     Atom = 0xb105
	Allowfullscreen           Atom = 0x2080f
	Allowpaymentrequest       Atom = 0xc113
	Allowusermedia            Atom = 0xdd0e
	Alt                       Atom = 0xf303
	Annotation                Atom = 0x1c90a
	AnnotationXml             Atom = 0x1c90e
	Applet                    Atom = 0x31906
	Area                      Atom = 0x35604
	Article                   Atom = 0x3fc07
	As                        Atom = 0x3c02
	Aside                     Atom = 0x10705
	Async                     Atom = 0xff05
	Audio                     Atom = 0x11505
	Autocomplete              Atom = 0x2780c
	Autofocus                 Atom = 0x12109
	Autoplay                  Atom = 0x13c08
	B                         Atom = 0x101
	Base                      Atom = 0x3b04
	Basefont                  Atom = 0x3b08
	Bdi                       Atom = 0xba03
	Bdo                       Atom = 0x14b03
	Bgsound                   Atom = 0x15e07
	Big                       Atom = 0x17003
	Blink                     Atom = 0x17305
	Blockquote                Atom = 0x1870a
	Body                      Atom = 0x2804
	Br                        Atom = 0x202
	Button                    Atom = 0x19106
	Canvas                    Atom = 0x10306
	Caption                   Atom = 0x23107
	Center                    Atom = 0x22006
	Challenge                 Atom = 0x29b09
	Charset                   Atom = 0x2107
	Checked                   Atom = 0x47907
	Cite                      Atom = 0x19c04
	Class                     Atom = 0x56405
	Code                      Atom = 0x5c504
	Col                       Atom = 0x1ab03
	Colgroup                  Atom = 0x1ab08
	Color                     Atom = 0x1bf05
	Cols                      Atom = 0x1c404
	Colspan                   Atom = 0x1c407
	Command                   Atom = 0x1d707
	Content                   Atom = 0x58b07
	Contenteditable           Atom = 0x58b0f
	Contextmenu               Atom = 0x3800b
	Controls                  Atom = 0x1de08
	Coords                    Atom = 0x1ea06
	Crossorigin               Atom = 0x1fb0b
	Data                      Atom = 0x4a504
	Datalist                  Atom = 0x4a508
	Datetime                  Atom = 0x2b808
	Dd                        Atom = 0x2d702
	Default                   Atom = 0x10a07
	Defer                     Atom = 0x5c705
	Del                       Atom = 0x45203
	Desc                      Atom = 0x56104
	Details                   Atom = 0x7207
	Dfn                       Atom = 0x8703
	Dialog                    Atom = 0xbb06
	Dir                       Atom = 0x9303
	Dirname                   Atom = 0x9307
	Disabled                  Atom = 0x16408
	Div                       Atom = 0x16b03
	Dl                        Atom = 0x5e602
	Download                  Atom = 0x46308
	Draggable                 Atom = 0x17a09
	Dropzone                  Atom = 0x40508
	Dt                        Atom = 0x64b02
	Em                        Atom = 0x6e02
	Embed                     Atom = 0x6e05
	Enctype                   Atom = 0x28d07
	Face                      Atom = 0x21e04
	Fieldset                  Atom = 0x22608
	Figcaption                Atom = 0x22e0a
	Figure                    Atom = 0x24806
	Font                      Atom = 0x3f04
	Footer                    Atom = 0xf606
	For                       Atom = 0x25403
	ForeignObject             Atom = 0x2540d
	Foreignobject             Atom = 0x2610d
	Form                      Atom = 0x26e04
	Formaction                Atom = 0x26e0a
	Formenctype               Atom = 0x2890b
	Formmethod                Atom = 0x2a40a
	Formnovalidate            Atom = 0x2ae0e
	Formtarget                Atom = 0x2c00a
	Frame                     Atom = 0x8b05
	Frameset                  Atom = 0x8b08
	H1                        Atom = 0x15c02
	H2                        Atom = 0x2de02
	H3                        Atom = 0x30d02
	H4                        Atom = 0x34502
	H5                        Atom = 0x34f02
	H6                        Atom = 0x64d02
	Head                      Atom = 0x33104
	Header                    Atom = 0x33106
	Headers                   Atom = 0x33107
	Height                    Atom = 0x5206
	Hgroup                    Atom = 0x2ca06
	Hidden                    Atom = 0x2d506
	High                      Atom = 0x2db04
	Hr                        Atom = 0x15702
	Href                      Atom = 0x2e004
	Hreflang                  Atom = 0x2e008
	Html                      Atom = 0x5604
	HttpEquiv                 Atom = 0x2e80a
	I                         Atom = 0x601
	Icon                      Atom = 0x58a04
	Id                        Atom = 0x10902
	Iframe                    Atom = 0x2fc06
	Image                     Atom = 0x30205
	Img                       Atom = 0x30703
	Input                     Atom = 0x44b05
	Inputmode                 Atom = 0x44b09
	Ins                       Atom = 0x20403
	Integrity                 Atom = 0x23f09
	Is                        Atom = 0x16502
	Isindex                   Atom = 0x30f07
	Ismap                     Atom = 0x31605
	Itemid                    Atom = 0x38b06
	Itemprop                  Atom = 0x19d08
	Itemref                   Atom = 0x3cd07
	Itemscope                 Atom = 0x67109
	Itemtype                  Atom = 0x31f08
	Kbd                       Atom = 0xb903
	Keygen                    Atom = 0x3206
	Keytype                   Atom = 0xd607
	Kind                      Atom = 0x17704
	Label                     Atom = 0x5905
	Lang                      Atom = 0x2e404
	Legend                    Atom = 0x18106
	Li                        Atom = 0xb202
	Link                      Atom = 0x17404
	List                      Atom = 0x4a904
	Listing                   Atom = 0x4a907
	Loop                      Atom = 0x5d04
	Low                       Atom = 0xc303
	Main                      Atom = 0x1004
	Malignmark                Atom = 0xb00a
	Manifest                  Atom = 0x6d708
	Map                       Atom = 0x31803
	Mark                      Atom = 0xb604
	Marquee                   Atom = 0x32707
	Math                      Atom = 0x32e04
	Max                       Atom = 0x33d03
	Maxlength                 Atom = 0x33d09
	Media                     Atom = 0xe605
	Mediagroup                Atom = 0xe60a
	Menu                      Atom = 0x38704
	Menuitem                  Atom = 0x38708
	Meta                      Atom = 0x4b804
	Meter                     Atom = 0x9805
	Method                    Atom = 0x2a806
	Mglyph                    Atom = 0x30806
	Mi                        Atom = 0x34702
	Min                       Atom = 0x34703
	Minlength                 Atom = 0x34709
	Mn                        Atom = 0x2b102
	Mo                        Atom = 0xa402
	Ms                        Atom = 0x67402
	Mtext                     Atom = 0x35105
	Multiple                  Atom = 0x35f08
	Muted                     Atom = 0x36705
	Name                      Atom = 0x9604
	Nav                       Atom = 0x1303
	Nobr                      Atom = 0x3704
	Noembed                   Atom = 0x6c07
	Noframes                  Atom = 0x8908
	Nomodule                  Atom = 0xa208
	Nonce                     Atom = 0x1a605
	Noscript                  Atom = 0x21608
	Novalidate                Atom = 0x2b20a
	Object                    Atom = 0x26806
	Ol                        Atom = 0x13702
	Onabort                   Atom = 0x19507
	Onafterprint              Atom = 0x2360c
	Onautocomplete            Atom = 0x2760e
	Onautocompleteerror       Atom = 0x27613
	Onauxclick                Atom = 0x61f0a
	Onbeforeprint             Atom = 0x69e0d
	Onbeforeunload            Atom = 0x6e70e
	Onblur                    Atom = 0x56d06
	Oncancel                  Atom = 0x11908
	Oncanplay                 Atom = 0x14d09
	Oncanplaythrough          Atom = 0x14d10
	Onchange                  Atom = 0x41b08
	Onclick                   Atom = 0x2f507
	Onclose                   Atom = 0x36c07
	Oncontextmenu             Atom = 0x37e0d
	Oncopy                    Atom = 0x39106
	Oncuechange               Atom = 0x3970b
	Oncut                     Atom = 0x3a205
	Ondblclick                Atom = 0x3a70a
	Ondrag                    Atom = 0x3b106
	Ondragend                 Atom = 0x3b109
	Ondragenter               Atom = 0x3ba0b
	Ondragexit                Atom = 0x3c50a
	Ondragleave               Atom = 0x3df0b
	Ondragover                Atom = 0x3ea0a
	Ondragstart               Atom = 0x3f40b
	Ondrop                    Atom = 0x40306
	Ondurationchange          Atom = 0x41310
	Onemptied                 Atom = 0x40a09
	Onended                   Atom = 0x42307
	Onerror                   Atom = 0x42a07
	Onfocus                   Atom = 0x43107
	Onhashchange              Atom = 0x43d0c
	Oninput                   Atom = 0x44907
	Oninvalid                 Atom = 0x45509
	Onkeydown                 Atom = 0x45e09
	Onkeypress                Atom = 0x46b0a
	Onkeyup                   Atom = 0x48007
	Onlanguagechange          Atom = 0x48d10
	Onload                    Atom = 0x49d06
	Onloadeddata              Atom = 0x49d0c
	Onloadedmetadata          Atom = 0x4b010
	Onloadend                 Atom = 0x4c609
	Onloadstart               Atom = 0x4cf0b
	Onmessage                 Atom = 0x4da09
	Onmessageerror            Atom = 0x4da0e
	Onmousedown               Atom = 0x4e80b
	Onmouseenter              Atom = 0x4f30c
	Onmouseleave              Atom = 0x4ff0c
	Onmousemove               Atom = 0x50b0b
	Onmouseout                Atom = 0x5160a
	Onmouseover               Atom = 0x5230b
	Onmouseup                 Atom = 0x52e09
	Onmousewheel              Atom = 0x53c0c
	Onoffline                 Atom = 0x54809
	Ononline                  Atom = 0x55108
	Onpagehide                Atom = 0x5590a
	Onpageshow                Atom = 0x5730a
	Onpaste                   Atom = 0x57f07
	Onpause                   Atom = 0x59a07
	Onplay                    Atom = 0x5a406
	Onplaying                 Atom = 0x5a409
	Onpopstate                Atom = 0x5ad0a
	Onprogress                Atom = 0x5b70a
	Onratechange              Atom = 0x5cc0c
	Onrejectionhandled        Atom = 0x5d812
	Onreset                   Atom = 0x5ea07
	Onresize                  Atom = 0x5f108
	Onscroll                  Atom = 0x60008
	Onsecuritypolicyviolation Atom = 0x60819
	Onseeked                  Atom = 0x62908
	Onseeking                 Atom = 0x63109
	Onselect                  Atom = 0x63a08
	Onshow                    Atom = 0x64406
	Onsort                    Atom = 0x64f06
	Onstalled                 Atom = 0x65909
	Onstorage                 Atom = 0x66209
	Onsubmit                  Atom = 0x66b08
	Onsuspend                 Atom = 0x67b09
	Ontimeupdate              Atom = 0x400c
	Ontoggle                  Atom = 0x68408
	Onunhandledrejection      Atom = 0x68c14
	Onunload                  Atom = 0x6ab08
	Onvolumechange            Atom = 0x6b30e
	Onwaiting                 Atom = 0x6c109
	Onwheel                   Atom = 0x6ca07
	Open                      Atom = 0x1a304
	Optgroup                  Atom = 0x5f08
	Optimum                   Atom = 0x6d107
	Option                    Atom = 0x6e306
	Output                    Atom = 0x51d06
	P                         Atom = 0xc01
	Param                     Atom = 0xc05
	Pattern                   Atom = 0x6607
	Picture                   Atom = 0x7b07
	Ping                      Atom = 0xef04
	Placeholder               Atom = 0x1310b
	Plaintext                 Atom = 0x1b209
	Playsinline               Atom = 0x1400b
	Poster                    Atom = 0x2cf06
	Pre                       Atom = 0x47003
	Preload                   Atom = 0x48607
	Progress                  Atom = 0x5b908
	Prompt                    Atom = 0x53606
	Public                    Atom = 0x58606
	Q                         Atom = 0xcf01
	Radiogroup                Atom = 0x30a
	Rb                        Atom = 0x3a02
	Readonly                  Atom = 0x35708
	Referrerpolicy            Atom = 0x3d10e
	Rel                       Atom = 0x48703
	Required                  Atom = 0x24c08
	Reversed                  Atom = 0x8008
	Rows                      Atom = 0x9c04
	Rowspan                   Atom = 0x9c07
	Rp                        Atom = 0x23c02
	Rt                        Atom = 0x19a02
	Rtc                       Atom = 0x19a03
	Ruby                      Atom = 0xfb04
	S                         Atom = 0x2501
	Samp                      Atom = 0x7804
	Sandbox                   Atom = 0x12907
	Scope                     Atom = 0x67505
	Scoped                    Atom = 0x67506
	Script                    Atom = 0x21806
	Seamless                  Atom = 0x37108
	Section                   Atom = 0x56807
	Select                    Atom = 0x63c06
	Selected                  Atom = 0x63c08
	Shape                     Atom = 0x1e505
	Size                      Atom = 0x5f504
	Sizes                     Atom = 0x5f505
	Slot                      Atom = 0x1ef04
	Small                     Atom = 0x20605
	Sortable                  Atom = 0x65108
	Sorted                    Atom = 0x33706
	Source                    Atom = 0x37806
	Spacer                    Atom = 0x43706
	Span                      Atom = 0x9f04
	Spellcheck                Atom = 0x4740a
	Src                       Atom = 0x5c003
	Srcdoc                    Atom = 0x5c006
	Srclang                   Atom = 0x5f907
	Srcset                    Atom = 0x6f906
	Start                     Atom = 0x3fa05
	Step                      Atom = 0x58304
	Strike                    Atom = 0xd206
	Strong                    Atom = 0x6dd06
	Style                     Atom = 0x6ff05
	Sub                       Atom = 0x66d03
	Summary                   Atom = 0x70407
	Sup                       Atom = 0x70b03
	Svg                       Atom = 0x70e03
	System                    Atom = 0x71106
	Tabindex                  Atom = 0x4be08
	Table                     Atom = 0x59505
	Target                    Atom = 0x2c406
	Tbody                     Atom = 0x2705
	Td                        Atom = 0x9202
	Template                  Atom = 0x71408
	Textarea                  Atom = 0x35208
	Tfoot                     Atom = 0xf505
	Th                        Atom = 0x15602
	Thead                     Atom = 0x33005
	Time                      Atom = 0x4204
	Title                     Atom = 0x11005
	Tr                        Atom = 0xcc02
	Track                     Atom = 0x1ba05
	Translate                 Atom = 0x1f209
	Tt                        Atom = 0x6802
	Type                      Atom = 0xd904
	Typemustmatch             Atom = 0x2900d
	U                         Atom = 0xb01
	Ul                        Atom = 0xa702
	Updateviacache            Atom = 0x460e
	Usemap                    Atom = 0x59e06
	Value                     Atom = 0x1505
	Var                       Atom = 0x16d03
	Video                     Atom = 0x2f105
	Wbr                       Atom = 0x57c03
	Width                     Atom = 0x64905
	Workertype                Atom = 0x71c0a
	Wrap                      Atom = 0x72604
	Xmp                       Atom = 0x12f03
)

const hash0 = 0x81cdf10e

const maxAtomLen = 25

var table = [1 << 9]Atom{
	0x1:   0xe60a,  // mediagroup
	0x2:   0x2e404, // lang
	0x4:   0x2c09,  // accesskey
	0x5:   0x8b08,  // frameset
	0x7:   0x63a08, // onselect
	0x8:   0x71106, // system
	0xa:   0x64905, // width
	0xc:   0x2890b, // formenctype
	0xd:   0x13702, // ol
	0xe:   0x3970b, // oncuechange
	0x10:  0x14b03, // bdo
	0x11:  0x11505, // audio
	0x12:  0x17a09, // draggable
	0x14:  0x2f105, // video
	0x15:  0x2b102, // mn
	0x16:  0x38704, // menu
	0x17:  0x2cf06, // poster
	0x19:  0xf606,  // footer
	0x1a:  0x2a806, // method
	0x1b:  0x2b808, // datetime
	0x1c:  0x19507, // onabort
	0x1d:  0x460e,  // updateviacache
	0x1e:  0xff05,  // async
	0x1f:  0x49d06, // onload
	0x21:  0x11908, // oncancel
	0x22:  0x62908, // onseeked
	0x23:  0x30205, // image
	0x24:  0x5d812, // onrejectionhandled
	0x26:  0x17404, // link
	0x27:  0x51d06, // output
	0x28:  0x33104, // head
	0x29:  0x4ff0c, // onmouseleave
	0x2a:  0x57f07, // onpaste
	0x2b:  0x5a409, // onplaying
	0x2c:  0x1c407, // colspan
	0x2f:  0x1bf05, // color
	0x30:  0x5f504, // size
	0x31:  0x2e80a, // http-equiv
	0x33:  0x601,   // i
	0x34:  0x5590a, // onpagehide
	0x35:  0x68c14, // onunhandledrejection
	0x37:  0x42a07, // onerror
	0x3a:  0x3b08,  // basefont
	0x3f:  0x1303,  // nav
	0x40:  0x17704, // kind
	0x41:  0x35708, // readonly
	0x42:  0x30806, // mglyph
	0x44:  0xb202,  // li
	0x46:  0x2d506, // hidden
	0x47:  0x70e03, // svg
	0x48:  0x58304, // step
	0x49:  0x23f09, // integrity
	0x4a:  0x58606, // public
	0x4c:  0x1ab03, // col
	0x4d:  0x1870a, // blockquote
	0x4e:  0x34f02, // h5
	0x50:  0x5b908, // progress
	0x51:  0x5f505, // sizes
	0x52:  0x34502, // h4
	0x56:  0x33005, // thead
	0x57:  0xd607,  // keytype
	0x58:  0x5b70a, // onprogress
	0x59:  0x44b09, // inputmode
	0x5a:  0x3b109, // ondragend
	0x5d:  0x3a205, // oncut
	0x5e:  0x43706, // spacer
	0x5f:  0x1ab08, // colgroup
	0x62:  0x16502, // is
	0x65:  0x3c02,  // as
	0x66:  0x54809, // onoffline
	0x67:  0x33706, // sorted
	0x69:  0x48d10, // onlanguagechange
	0x6c:  0x43d0c, // onhashchange
	0x6d:  0x9604,  // name
	0x6e:  0xf505,  // tfoot
	0x6f:  0x56104, // desc
	0x70:  0x33d03, // max
	0x72:  0x1ea06, // coords
	0x73:  0x30d02, // h3
	0x74:  0x6e70e, // onbeforeunload
	0x75:  0x9c04,  // rows
	0x76:  0x63c06, // select
	0x77:  0x9805,  // meter
	0x78:  0x38b06, // itemid
	0x79:  0x53c0c, // onmousewheel
	0x7a:  0x5c006, // srcdoc
	0x7d:  0x1ba05, // track
	0x7f:  0x31f08, // itemtype
	0x82:  0xa402,  // mo
	0x83:  0x41b08, // onchange
	0x84:  0x33107, // headers
	0x85:  0x5cc0c, // onratechange
	0x86:  0x60819, // onsecuritypolicyviolation
	0x88:  0x4a508, // datalist
	0x89:  0x4e80b, // onmousedown
	0x8a:  0x1ef04, // slot
	0x8b:  0x4b010, // onloadedmetadata
	0x8c:  0x1a06,  // accept
	0x8d:  0x26806, // object
	0x91:  0x6b30e, // onvolumechange
	0x92:  0x2107,  // charset
	0x93:  0x27613, // onautocompleteerror
	0x94:  0xc113,  // allowpaymentrequest
	0x95:  0x2804,  // body
	0x96:  0x10a07, // default
	0x97:  0x63c08, // selected
	0x98:  0x21e04, // face
	0x99:  0x1e505, // shape
	0x9b:  0x68408, // ontoggle
	0x9e:  0x64b02, // dt
	0x9f:  0xb604,  // mark
	0xa1:  0xb01,   // u
	0xa4:  0x6ab08, // onunload
	0xa5:  0x5d04,  // loop
	0xa6:  0x16408, // disabled
	0xaa:  0x42307, // onended
	0xab:  0xb00a,  // malignmark
	0xad:  0x67b09, // onsuspend
	0xae:  0x35105, // mtext
	0xaf:  0x64f06, // onsort
	0xb0:  0x19d08, // itemprop
	0xb3:  0x67109, // itemscope
	0xb4:  0x17305, // blink
	0xb6:  0x3b106, // ondrag
	0xb7:  0xa702,  // ul
	0xb8:  0x26e04, // form
	0xb9:  0x12907, // sandbox
	0xba:  0x8b05,  // frame
	0xbb:  0x1505,  // value
	0xbc:  0x66209, // onstorage
	0xbf:  0xaa07,  // acronym
	0xc0:  0x19a02, // rt
	0xc2:  0x202,   // br
	0xc3:  0x22608, // fieldset
	0xc4:  0x2900d, // typemustmatch
	0xc5:  0xa208,  // nomodule
	0xc6:  0x6c07,  // noembed
	0xc7:  0x69e0d, // onbeforeprint
	0xc8:  0x19106, // button
	0xc9:  0x2f507, // onclick
	0xca:  0x70407, // summary
	0xcd:  0xfb04,  // ruby
	0xce:  0x56405, // class
	0xcf:  0x3f40b, // ondragstart
	0xd0:  0x23107, // caption
	0xd4:  0xdd0e,  // allowusermedia
	0xd5:  0x4cf0b, // onloadstart
	0xd9:  0x16b03, // div
	0xda:  0x4a904, // list
	0xdb:  0x32e04, // math
	0xdc:  0x44b05, // input
	0xdf:  0x3ea0a, // ondragover
	0xe0:  0x2de02, // h2
	0xe2:  0x1b209, // plaintext
	0xe4:  0x4f30c, // onmouseenter
	0xe7:  0x47907, // checked
	0xe8:  0x47003, // pre
	0xea:  0x35f08, // multiple
	0xeb:  0xba03,  // bdi
	0xec:  0x33d09, // maxlength
	0xed:  0xcf01,  // q
	0xee:  0x61f0a, // onauxclick
	0xf0:  0x57c03, // wbr
	0xf2:  0x3b04,  // base
	0xf3:  0x6e306, // option
	0xf5:  0x41310, // ondurationchange
	0xf7:  0x8908,  // noframes
	0xf9:  0x40508, // dropzone
	0xfb:  0x67505, // scope
	0xfc:  0x8008,  // reversed
	0xfd:  0x3ba0b, // ondragenter
	0xfe:  0x3fa05, // start
	0xff:  0x12f03, // xmp
	0x100: 0x5f907, // srclang
	0x101: 0x30703, // img
	0x104: 0x101,   // b
	0x105: 0x25403, // for
	0x106: 0x10705, // aside
	0x107: 0x44907, // oninput
	0x108: 0x35604, // area
	0x109: 0x2a40a, // formmethod
	0x10a: 0x72604, // wrap
	0x10c: 0x23c02, // rp
	0x10d: 0x46b0a, // onkeypress
	0x10e: 0x6802,  // tt
	0x110: 0x34702, // mi
	0x111: 0x36705, // muted
	0x112: 0xf303,  // alt
	0x113: 0x5c504, // code
	0x114: 0x6e02,  // em
	0x115: 0x3c50a, // ondragexit
	0x117: 0x9f04,  // span
	0x119: 0x6d708, // manifest
	0x11a: 0x38708, // menuitem
	0x11b: 0x58b07, // content
	0x11d: 0x6c109, // onwaiting
	0x11f: 0x4c609, // onloadend
	0x121: 0x37e0d, // oncontextmenu
	0x123: 0x56d06, // onblur
	0x124: 0x3fc07, // article
	0x125: 0x9303,  // dir
	0x126: 0xef04,  // ping
	0x127: 0x24c08, // required
	0x128: 0x45509, // oninvalid
	0x129: 0xb105,  // align
	0x12b: 0x58a04, // icon
	0x12c: 0x64d02, // h6
	0x12d: 0x1c404, // cols
	0x12e: 0x22e0a, // figcaption
	0x12f: 0x45e09, // onkeydown
	0x130: 0x66b08, // onsubmit
	0x131: 0x14d09, // oncanplay
	0x132: 0x70b03, // sup
	0x133: 0xc01,   // p
	0x135: 0x40a09, // onemptied
	0x136: 0x39106, // oncopy
	0x137: 0x19c04, // cite
	0x138: 0x3a70a, // ondblclick
	0x13a: 0x50b0b, // onmousemove
	0x13c: 0x66d03, // sub
	0x13d: 0x48703, // rel
	0x13e: 0x5f08,  // optgroup
	0x142: 0x9c07,  // rowspan
	0x143: 0x37806, // source
	0x144: 0x21608, // noscript
	0x145: 0x1a304, // open
	0x146: 0x20403, // ins
	0x147: 0x2540d, // foreignObject
	0x148: 0x5ad0a, // onpopstate
	0x14a: 0x28d07, // enctype
	0x14b: 0x2760e, // onautocomplete
	0x14c: 0x35208, // textarea
	0x14e: 0x2780c, // autocomplete
	0x14f: 0x15702, // hr
	0x150: 0x1de08, // controls
	0x151: 0x10902, // id
	0x153: 0x2360c, // onafterprint
	0x155: 0x2610d, // foreignobject
	0x156: 0x32707, // marquee
	0x157: 0x59a07, // onpause
	0x158: 0x5e602, // dl
	0x159: 0x5206,  // height
	0x15a: 0x34703, // min
	0x15b: 0x9307,  // dirname
	0x15c: 0x1f209, // translate
	0x15d: 0x5604,  // html
	0x15e: 0x34709, // minlength
	0x15f: 0x48607, // preload
	0x160: 0x71408, // template
	0x161: 0x3df0b, // ondragleave
	0x162: 0x3a02,  // rb
	0x164: 0x5c003, // src
	0x165: 0x6dd06, // strong
	0x167: 0x7804,  // samp
	0x168: 0x6f307, // address
	0x169: 0x55108, // ononline
	0x16b: 0x1310b, // placeholder
	0x16c: 0x2c406, // target
	0x16d: 0x20605, // small
	0x16e: 0x6ca07, // onwheel
	0x16f: 0x1c90a, // annotation
	0x170: 0x4740a, // spellcheck
	0x171: 0x7207,  // details
	0x172: 0x10306, // canvas
	0x173: 0x12109, // autofocus
	0x174: 0xc05,   // param
	0x176: 0x46308, // download
	0x177: 0x45203, // del
	0x178: 0x36c07, // onclose
	0x179: 0xb903,  // kbd
	0x17a: 0x31906, // applet
	0x17b: 0x2e004, // href
	0x17c: 0x5f108, // onresize
	0x17e: 0x49d0c, // onloadeddata
	0x180: 0xcc02,  // tr
	0x181: 0x2c00a, // formtarget
	0x182: 0x11005, // title
	0x183: 0x6ff05, // style
	0x184: 0xd206,  // strike
	0x185: 0x59e06, // usemap
	0x186: 0x2fc06, // iframe
	0x187: 0x1004,  // main
	0x189: 0x7b07,  // picture
	0x18c: 0x31605, // ismap
	0x18e: 0x4a504, // data
	0x18f: 0x5905,  // label
	0x191: 0x3d10e, // referrerpolicy
	0x192: 0x15602, // th
	0x194: 0x53606, // prompt
	0x195: 0x56807, // section
	0x197: 0x6d107, // optimum
	0x198: 0x2db04, // high
	0x199: 0x15c02, // h1
	0x19a: 0x65909, // onstalled
	0x19b: 0x16d03, // var
	0x19c: 0x4204,  // time
	0x19e: 0x67402, // ms
	0x19f: 0x33106, // header
	0x1a0: 0x4da09, // onmessage
	0x1a1: 0x1a605, // nonce
	0x1a2: 0x26e0a, // formaction
	0x1a3: 0x22006, // center
	0x1a4: 0x3704,  // nobr
	0x1a5: 0x59505, // table
	0x1a6: 0x4a907, // listing
	0x1a7: 0x18106, // legend
	0x1a9: 0x29b09, // challenge
	0x1aa: 0x24806, // figure
	0x1ab: 0xe605,  // media
	0x1ae: 0xd904,  // type
	0x1af: 0x3f04,  // font
	0x1b0: 0x4da0e, // onmessageerror
	0x1b1: 0x37108, // seamless
	0x1b2: 0x8703,  // dfn
	0x1b3: 0x5c705, // defer
	0x1b4: 0xc303,  // low
	0x1b5: 0x19a03, // rtc
	0x1b6: 0x5230b, // onmouseover
	0x1b7: 0x2b20a, // novalidate
	0x1b8: 0x71c0a, // workertype
	0x1ba: 0x3cd07, // itemref
	0x1bd: 0x1,     // a
	0x1be: 0x31803, // map
	0x1bf: 0x400c,  // ontimeupdate
	0x1c0: 0x15e07, // bgsound
	0x1c1: 0x3206,  // keygen
	0x1c2: 0x2705,  // tbody
	0x1c5: 0x64406, // onshow
	0x1c7: 0x2501,  // s
	0x1c8: 0x6607,  // pattern
	0x1cc: 0x14d10, // oncanplaythrough
	0x1ce: 0x2d702, // dd
	0x1cf: 0x6f906, // srcset
	0x1d0: 0x17003, // big
	0x1d2: 0x65108, // sortable
	0x1d3: 0x48007, // onkeyup
	0x1d5: 0x5a406, // onplay
	0x1d7: 0x4b804, // meta
	0x1d8: 0x40306, // ondrop
	0x1da: 0x60008, // onscroll
	0x1db: 0x1fb0b, // crossorigin
	0x1dc: 0x5730a, // onpageshow
	0x1dd: 0x4,     // abbr
	0x1de: 0x9202,  // td
	0x1df: 0x58b0f, // contenteditable
	0x1e0: 0x27206, // action
	0x1e1: 0x1400b, // playsinline
	0x1e2: 0x43107, // onfocus
	0x1e3: 0x2e008, // hreflang
	0x1e5: 0x5160a, // onmouseout
	0x1e6: 0x5ea07, // onreset
	0x1e7: 0x13c08, // autoplay
	0x1e8: 0x63109, // onseeking
	0x1ea: 0x67506, // scoped
	0x1ec: 0x30a,   // radiogroup
	0x1ee: 0x3800b, // contextmenu
	0x1ef: 0x52e09, // onmouseup
	0x1f1: 0x2ca06, // hgroup
	0x1f2: 0x2080f, // allowfullscreen
	0x1f3: 0x4be08, // tabindex
	0x1f6: 0x30f07, // isindex
	0x1f7: 0x1a0e,  // accept-charset
	0x1f8: 0x2ae0e, // formnovalidate
	0x1fb: 0x1c90e, // annotation-xml
	0x1fc: 0x6e05,  // embed
	0x1fd: 0x21806, // script
	0x1fe: 0xbb06,  // dialog
	0x1ff: 0x1d707, // command
}

const atomText = "abbradiogrouparamainavalueaccept-charsetbodyaccesskeygenobrb" +
	"asefontimeupdateviacacheightmlabelooptgroupatternoembedetail" +
	"sampictureversedfnoframesetdirnameterowspanomoduleacronymali" +
	"gnmarkbdialogallowpaymentrequestrikeytypeallowusermediagroup" +
	"ingaltfooterubyasyncanvasidefaultitleaudioncancelautofocusan" +
	"dboxmplaceholderautoplaysinlinebdoncanplaythrough1bgsoundisa" +
	"bledivarbigblinkindraggablegendblockquotebuttonabortcitempro" +
	"penoncecolgrouplaintextrackcolorcolspannotation-xmlcommandco" +
	"ntrolshapecoordslotranslatecrossoriginsmallowfullscreenoscri" +
	"ptfacenterfieldsetfigcaptionafterprintegrityfigurequiredfore" +
	"ignObjectforeignobjectformactionautocompleteerrorformenctype" +
	"mustmatchallengeformmethodformnovalidatetimeformtargethgroup" +
	"osterhiddenhigh2hreflanghttp-equivideonclickiframeimageimgly" +
	"ph3isindexismappletitemtypemarqueematheadersortedmaxlength4m" +
	"inlength5mtextareadonlymultiplemutedoncloseamlessourceoncont" +
	"extmenuitemidoncopyoncuechangeoncutondblclickondragendondrag" +
	"enterondragexitemreferrerpolicyondragleaveondragoverondragst" +
	"articleondropzonemptiedondurationchangeonendedonerroronfocus" +
	"paceronhashchangeoninputmodeloninvalidonkeydownloadonkeypres" +
	"spellcheckedonkeyupreloadonlanguagechangeonloadeddatalisting" +
	"onloadedmetadatabindexonloadendonloadstartonmessageerroronmo" +
	"usedownonmouseenteronmouseleaveonmousemoveonmouseoutputonmou" +
	"seoveronmouseupromptonmousewheelonofflineononlineonpagehides" +
	"classectionbluronpageshowbronpastepublicontenteditableonpaus" +
	"emaponplayingonpopstateonprogressrcdocodeferonratechangeonre" +
	"jectionhandledonresetonresizesrclangonscrollonsecuritypolicy" +
	"violationauxclickonseekedonseekingonselectedonshowidth6onsor" +
	"tableonstalledonstorageonsubmitemscopedonsuspendontoggleonun" +
	"handledrejectionbeforeprintonunloadonvolumechangeonwaitingon" +
	"wheeloptimumanifestrongoptionbeforeunloaddressrcsetstylesumm" +
	"arysupsvgsystemplateworkertypewrap"
