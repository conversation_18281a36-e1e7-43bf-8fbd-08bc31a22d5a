# mfa sdk

## what you need to do

1. register your app to get
    - `ak`
    - `sk`
2. prepare user id
    - `company uid` or `wps uid` or `username`
3. follow the example and enjoy ~

## example

```golang
package main

import (
   "ksogit.kingsoft.net/security-pub/mfasdk"
)

func main() {
   ak := "ak"
   sk := "sk"
   client := mfasdk.New(mfasdk.WithAKSK(ak, sk))

   // 在xiezuo中进行动态令牌通知
   if err := client.Notice(mfasdk.NoticeParam{
      CompanyUID: "xxxxxxx",
   }); err != nil {
      panic(err)
   }

   // 验证动态令牌
   ok, err := client.Verify(mfasdk.VerifyParam{
      CompanyUID: "xxxxxxx",
      Code:       "000000",
   })
   if err != nil {
      panic(err)
   }
   println(ok)
}
```