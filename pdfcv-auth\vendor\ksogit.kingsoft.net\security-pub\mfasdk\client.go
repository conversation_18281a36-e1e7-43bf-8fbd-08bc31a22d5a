package mfasdk

import (
	"time"

	"github.com/go-playground/validator/v10"
	"ksogit.kingsoft.net/security-pub/mfasdk/httpclientsdk"
)

type (
	// Client ...
	Client struct {
		config     *Config
		validator  *validator.Validate
		httpClient *httpclientsdk.Client
	}
	// Config ...
	Config struct {
		BaseURL string
		AK      string
		SK      string
	}
)

// New ...
func New(setters ...Setter) *Client {
	readTimeout, _ := time.ParseDuration("30s")
	writeTimeout, _ := time.ParseDuration("30s")
	maxIdleConnDuration, _ := time.ParseDuration("1h")
	c := Client{
		config: &Config{
			BaseURL: defaultBaseURL,
		},
		validator: validator.New(),
		httpClient: httpclientsdk.New(
			httpclientsdk.WithTimeoutConfig(
				readTimeout,
				writeTimeout,
				maxIdleConnDuration,
			),
		),
	}
	for _, setter := range setters {
		setter(&c)
	}
	return &c
}
