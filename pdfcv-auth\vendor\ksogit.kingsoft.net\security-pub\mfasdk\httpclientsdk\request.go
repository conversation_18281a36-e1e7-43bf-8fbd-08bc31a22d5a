package httpclientsdk

import (
	"fmt"
	"net/http"

	"github.com/valyala/fasthttp"
)

// RequestWithResponse ...
//
//	Warning: if you want to use a json body, don't forget to add a application/json header
func (cli *Client) RequestWithResponse(param Param) (*Response, error) {
	req := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(req)
	// method
	req.Header.SetMethod(param.Method)
	// uri
	req.SetRequestURI(param.URI)
	// body
	if len(param.Body) > 0 {
		req.SetBody(param.Body)
	}
	// header
	if len(param.Header) > 0 {
		for k, v := range param.Header {
			req.Header.Set(k, v)
		}
	}
	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)
	if err := cli.httpClient.Do(req, resp); err != nil {
		return nil, err
	}
	return &Response{
		statusCode: resp.StatusCode(),
		body:       resp.Body(),
	}, nil
}

// Request ...
func (cli *Client) Request(param Param) error {
	resp, err := cli.RequestWithResponse(param)
	if err != nil {
		return err
	}
	if resp.StatusCode() != http.StatusOK {
		return fmt.Errorf("http request failed, status code: %d, body: %s", resp.StatusCode(), string(resp.Body()))
	}
	return nil
}
