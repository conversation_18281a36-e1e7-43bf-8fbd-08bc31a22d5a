package httpclientsdk

import (
	"crypto/tls"
	"time"
)

// Setter ...
type Setter func(client *Client)

// WithTLSConfig ...
func WithTLSConfig(config *tls.Config) Setter {
	return func(client *Client) {
		client.httpClient.TLSConfig = config
	}
}

// WithTimeoutConfig ...
func WithTimeoutConfig(readTimeout, writeTimeout, maxIdleConnDuration time.Duration) Setter {
	return func(client *Client) {
		client.httpClient.ReadTimeout = readTimeout
		client.httpClient.WriteTimeout = writeTimeout
		client.httpClient.MaxIdleConnDuration = maxIdleConnDuration
	}
}
