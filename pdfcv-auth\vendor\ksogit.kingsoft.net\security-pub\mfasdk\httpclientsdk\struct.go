package httpclientsdk

// Param ...
type Param struct {
	Method string            `json:"method"`
	URI    string            `json:"uri"`
	Body   []byte            `json:"body"`
	Header map[string]string `json:"header"`
}

// Response ...
type Response struct {
	statusCode int
	body       []byte
}

// StatusCode ...
func (r *Response) StatusCode() int {
	return r.statusCode
}

// Body ...
func (r *Response) Body() []byte {
	return r.body
}
