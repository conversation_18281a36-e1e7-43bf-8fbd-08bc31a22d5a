package mfasdk

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"ksogit.kingsoft.net/security-pub/mfasdk/httpclientsdk"
)

const (
	defaultBaseURL    = "https://mfa.kwps.cn"
	authHeader        = "X-Auth"
	contentTypeHeader = "Content-Type"
	dateHeader        = "Date"
	jsonContentType   = "application/json"
	signPrefix        = "MFA"
)

const (
	errorOperationReachLimitKind = 10005
	errorCodeIncorrectKind       = 10008
	errorUserNotFoundKind        = 10009
)

type errResp struct {
	Kind int    `json:"kind,omitempty"`
	Desc string `json:"desc"`
}

// VerifyParam ...
//
//	CompanyUID 从 https://open-xz.wps.cn/ 获取, 优先使用 CompanyUID 获取用户信息
//	WpsUID     从 wps 获取, 如果 CompanyUID 为空, 使用 WpsUID 获取用户信息
//	Username   金山邮箱前缀
//	Code       从 https://mfa.kwps.cn/ 获取
type VerifyParam struct {
	CompanyUID string `json:"company_uid,omitempty" validate:"omitempty,min=1,max=100"`
	WpsUID     int64  `json:"wps_uid,omitempty" validate:"omitempty,min=1"`
	Username   string `json:"username,omitempty" validate:"omitempty,min=1,max=100"`
	Code       string `json:"code" validate:"required,len=6"`
}

// Verify ...
func (cli *Client) Verify(param VerifyParam) (ok bool, err error) {
	if err := cli.validator.Struct(param); err != nil {
		return false, errors.New("invalid param")
	}
	if len(param.CompanyUID) == 0 && param.WpsUID == 0 && len(param.Username) == 0 {
		return false, errors.New("invalid param")
	}

	bs, err := json.Marshal(param)
	if err != nil {
		return false, err
	}
	var (
		path   = "/api/mfa/verify"
		uri    = fmt.Sprintf("%s%s", cli.config.BaseURL, path)
		header = buildHeader(cli.config.AK, cli.config.SK, path, bs)
	)
	resp, err := cli.httpClient.RequestWithResponse(httpclientsdk.Param{
		Method: http.MethodPost,
		URI:    uri,
		Body:   bs,
		Header: header,
	})
	if err != nil {
		return false, err
	}
	switch resp.StatusCode() {
	case http.StatusOK:
		return true, nil
	default:
		var ep errResp
		if err := json.Unmarshal(resp.Body(), &ep); err != nil {
			return false, err
		}
		switch ep.Kind {
		case errorCodeIncorrectKind:
			return false, nil
		case errorUserNotFoundKind:
			return false, ErrUserNotFound
		default:
			return false, errors.New(ep.Desc)
		}
	}
}

// NoticeParam ...
//
//	CompanyUID 从 https://open-xz.wps.cn/ 获取, 优先使用 CompanyUID 获取用户信息
//	WpsUID     从 wps 获取, 如果 CompanyUID 为空, 使用 WpsUID 获取用户信息
//	Username   金山邮箱前缀
type NoticeParam struct {
	CompanyUID string `json:"company_uid,omitempty" validate:"omitempty,min=1,max=100"`
	WpsUID     int64  `json:"wps_uid,omitempty" validate:"omitempty,min=1"`
	Username   string `json:"username,omitempty" validate:"omitempty,min=1,max=100"`
}

// Notice ...
func (cli *Client) Notice(param NoticeParam) error {
	if err := cli.validator.Struct(param); err != nil {
		return errors.New("invalid param")
	}
	if len(param.CompanyUID) == 0 && param.WpsUID == 0 && len(param.Username) == 0 {
		return errors.New("invalid param")
	}

	bs, err := json.Marshal(param)
	if err != nil {
		return err
	}
	var (
		path   = "/api/mfa/notice"
		uri    = fmt.Sprintf("%s%s", cli.config.BaseURL, path)
		header = buildHeader(cli.config.AK, cli.config.SK, path, bs)
	)
	resp, err := cli.httpClient.RequestWithResponse(httpclientsdk.Param{
		Method: http.MethodPost,
		URI:    uri,
		Body:   bs,
		Header: header,
	})
	if err != nil {
		return err
	}
	switch resp.StatusCode() {
	case http.StatusOK:
		return nil
	default:
		var ep errResp
		if err := json.Unmarshal(resp.Body(), &ep); err != nil {
			return err
		}
		switch ep.Kind {
		case errorOperationReachLimitKind:
			return ErrOperationReachLimit
		case errorUserNotFoundKind:
			return ErrUserNotFound
		default:
			return errors.New(ep.Desc)
		}
	}
}

func buildHeader(ak, sk, requestURI string, body []byte) map[string]string {
	header := make(map[string]string)
	date := time.Now().UTC().Format(http.TimeFormat)
	header[contentTypeHeader] = jsonContentType
	header[dateHeader] = date
	header[authHeader] = buildAuthSign(ak, sk, jsonContentType, date, requestURI, body)
	return header
}

func buildAuthSign(ak, sk, contentType, date, requestURI string, body []byte) string {
	h := hmac.New(sha256.New, []byte(sk))
	h.Write([]byte(contentType))
	h.Write([]byte(date))
	h.Write([]byte(requestURI))
	if len(body) <= 0 {
		body = []byte("null")
	}
	h.Write(body)
	return fmt.Sprintf("%s:%s:%s", signPrefix, ak, base64.StdEncoding.EncodeToString(h.Sum(nil)))
}
