# github.com/BurntSushi/toml v0.3.1
## explicit
github.com/BurntSushi/toml
# github.com/Knetic/govaluate v3.0.1-0.20171022003610-9aa49832a739+incompatible
## explicit
github.com/Knetic/govaluate
# github.com/alibaba/sentinel-golang v1.0.4
## explicit; go 1.13
github.com/alibaba/sentinel-golang/api
github.com/alibaba/sentinel-golang/core/base
github.com/alibaba/sentinel-golang/core/circuitbreaker
github.com/alibaba/sentinel-golang/core/config
github.com/alibaba/sentinel-golang/core/flow
github.com/alibaba/sentinel-golang/core/hotspot
github.com/alibaba/sentinel-golang/core/hotspot/cache
github.com/alibaba/sentinel-golang/core/isolation
github.com/alibaba/sentinel-golang/core/log
github.com/alibaba/sentinel-golang/core/log/metric
github.com/alibaba/sentinel-golang/core/stat
github.com/alibaba/sentinel-golang/core/stat/base
github.com/alibaba/sentinel-golang/core/system
github.com/alibaba/sentinel-golang/core/system_metric
github.com/alibaba/sentinel-golang/exporter/metric
github.com/alibaba/sentinel-golang/exporter/metric/prometheus
github.com/alibaba/sentinel-golang/logging
github.com/alibaba/sentinel-golang/util
# github.com/alibaba/sentinel-golang/pkg/adapters/gin v0.0.0-20221011112204-0d804bbadda5
## explicit; go 1.13
github.com/alibaba/sentinel-golang/pkg/adapters/gin
# github.com/aliyun/aliyun-oss-go-sdk v2.2.6+incompatible
## explicit
github.com/aliyun/aliyun-oss-go-sdk/oss
# github.com/andeya/goutil v0.0.0-20220704075712-42f2ec55fe8d
## explicit
github.com/andeya/goutil
github.com/andeya/goutil/errors
# github.com/beorn7/perks v1.0.1
## explicit; go 1.11
github.com/beorn7/perks/quantile
# github.com/bitly/go-simplejson v0.5.0
## explicit
github.com/bitly/go-simplejson
# github.com/boombuler/barcode v1.0.1-0.20190219062509-6c824513bacc
## explicit
github.com/boombuler/barcode
github.com/boombuler/barcode/qr
github.com/boombuler/barcode/utils
# github.com/bsm/redislock v0.8.2
## explicit; go 1.17
github.com/bsm/redislock
# github.com/bytedance/go-tagexpr/v2 v2.9.6
## explicit; go 1.14
github.com/bytedance/go-tagexpr/v2
github.com/bytedance/go-tagexpr/v2/validator
# github.com/casbin/casbin/v2 v2.61.1
## explicit; go 1.13
github.com/casbin/casbin/v2
github.com/casbin/casbin/v2/config
github.com/casbin/casbin/v2/constant
github.com/casbin/casbin/v2/effector
github.com/casbin/casbin/v2/errors
github.com/casbin/casbin/v2/log
github.com/casbin/casbin/v2/model
github.com/casbin/casbin/v2/persist
github.com/casbin/casbin/v2/persist/cache
github.com/casbin/casbin/v2/persist/file-adapter
github.com/casbin/casbin/v2/rbac
github.com/casbin/casbin/v2/rbac/default-role-manager
github.com/casbin/casbin/v2/util
# github.com/cespare/xxhash/v2 v2.1.2
## explicit; go 1.11
github.com/cespare/xxhash/v2
# github.com/chanxuehong/rand v0.0.0-20201110082127-2f19a1bdd973
## explicit; go 1.15
github.com/chanxuehong/rand
# github.com/chanxuehong/wechat v0.0.0-20201110083048-0180211b69fd
## explicit; go 1.15
github.com/chanxuehong/wechat/internal/debug/api
github.com/chanxuehong/wechat/oauth2
github.com/chanxuehong/wechat/util
# github.com/cpuguy83/go-md2man/v2 v2.0.2
## explicit; go 1.11
github.com/cpuguy83/go-md2man/v2/md2man
# github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f
## explicit
github.com/dgryski/go-rendezvous
# github.com/disintegration/imaging v1.6.2
## explicit
github.com/disintegration/imaging
# github.com/fatih/color v1.7.0
## explicit
github.com/fatih/color
# github.com/fsnotify/fsnotify v1.4.9
## explicit; go 1.13
github.com/fsnotify/fsnotify
# github.com/ghodss/yaml v1.0.0
## explicit
github.com/ghodss/yaml
# github.com/gin-contrib/sse v0.1.0
## explicit; go 1.12
github.com/gin-contrib/sse
# github.com/gin-gonic/gin v1.8.2
## explicit; go 1.18
github.com/gin-gonic/gin
github.com/gin-gonic/gin/binding
github.com/gin-gonic/gin/internal/bytesconv
github.com/gin-gonic/gin/internal/json
github.com/gin-gonic/gin/render
# github.com/git-chglog/git-chglog v0.0.0-20190611050339-63a4e637021f
## explicit
github.com/git-chglog/git-chglog
github.com/git-chglog/git-chglog/cmd/git-chglog
# github.com/go-admin-team/go-admin-core v1.5.1
## explicit; go 1.18
github.com/go-admin-team/go-admin-core/config
github.com/go-admin-team/go-admin-core/config/encoder
github.com/go-admin-team/go-admin-core/config/encoder/json
github.com/go-admin-team/go-admin-core/config/encoder/toml
github.com/go-admin-team/go-admin-core/config/encoder/xml
github.com/go-admin-team/go-admin-core/config/encoder/yaml
github.com/go-admin-team/go-admin-core/config/loader
github.com/go-admin-team/go-admin-core/config/loader/memory
github.com/go-admin-team/go-admin-core/config/reader
github.com/go-admin-team/go-admin-core/config/reader/json
github.com/go-admin-team/go-admin-core/config/source
github.com/go-admin-team/go-admin-core/config/source/file
github.com/go-admin-team/go-admin-core/debug/log
github.com/go-admin-team/go-admin-core/debug/writer
github.com/go-admin-team/go-admin-core/logger
github.com/go-admin-team/go-admin-core/storage
github.com/go-admin-team/go-admin-core/storage/cache
github.com/go-admin-team/go-admin-core/storage/locker
github.com/go-admin-team/go-admin-core/storage/queue
github.com/go-admin-team/go-admin-core/tools/database
github.com/go-admin-team/go-admin-core/tools/gorm/logger
github.com/go-admin-team/go-admin-core/tools/language
github.com/go-admin-team/go-admin-core/tools/search
github.com/go-admin-team/go-admin-core/tools/transfer
# github.com/go-admin-team/go-admin-core/plugins/logger/zap v0.0.0-20210610020726-2db73adb505d
## explicit; go 1.14
github.com/go-admin-team/go-admin-core/plugins/logger/zap
# github.com/go-admin-team/go-admin-core/sdk v1.5.1
## explicit; go 1.18
github.com/go-admin-team/go-admin-core/sdk
github.com/go-admin-team/go-admin-core/sdk/api
github.com/go-admin-team/go-admin-core/sdk/config
github.com/go-admin-team/go-admin-core/sdk/pkg
github.com/go-admin-team/go-admin-core/sdk/pkg/captcha
github.com/go-admin-team/go-admin-core/sdk/pkg/casbin
github.com/go-admin-team/go-admin-core/sdk/pkg/jwtauth
github.com/go-admin-team/go-admin-core/sdk/pkg/jwtauth/user
github.com/go-admin-team/go-admin-core/sdk/pkg/logger
github.com/go-admin-team/go-admin-core/sdk/pkg/response
github.com/go-admin-team/go-admin-core/sdk/pkg/utils
github.com/go-admin-team/go-admin-core/sdk/pkg/ws
github.com/go-admin-team/go-admin-core/sdk/runtime
github.com/go-admin-team/go-admin-core/sdk/service
# github.com/go-admin-team/gorm-adapter/v3 v3.2.1-0.20210902112335-4148cb356a24
## explicit; go 1.14
github.com/go-admin-team/gorm-adapter/v3
# github.com/go-admin-team/redis-watcher/v2 v2.0.0-20221121052608-058cebff72c2
## explicit; go 1.18
github.com/go-admin-team/redis-watcher/v2
# github.com/go-admin-team/redisqueue/v2 v2.0.0
## explicit; go 1.12
github.com/go-admin-team/redisqueue/v2
# github.com/go-ole/go-ole v1.2.6
## explicit; go 1.12
github.com/go-ole/go-ole
github.com/go-ole/go-ole/oleutil
# github.com/go-playground/locales v0.14.0
## explicit; go 1.13
github.com/go-playground/locales
github.com/go-playground/locales/currency
github.com/go-playground/locales/en
github.com/go-playground/locales/zh
# github.com/go-playground/universal-translator v0.18.0
## explicit; go 1.13
github.com/go-playground/universal-translator
# github.com/go-playground/validator/v10 v10.11.1
## explicit; go 1.13
github.com/go-playground/validator/v10
github.com/go-playground/validator/v10/translations/en
github.com/go-playground/validator/v10/translations/zh
# github.com/go-redis/redis/v7 v7.4.1
## explicit; go 1.11
github.com/go-redis/redis/v7
github.com/go-redis/redis/v7/internal
github.com/go-redis/redis/v7/internal/consistenthash
github.com/go-redis/redis/v7/internal/hashtag
github.com/go-redis/redis/v7/internal/pool
github.com/go-redis/redis/v7/internal/proto
github.com/go-redis/redis/v7/internal/util
# github.com/go-redis/redis/v9 v9.0.0-rc.1
## explicit; go 1.17
github.com/go-redis/redis/v9
github.com/go-redis/redis/v9/internal
github.com/go-redis/redis/v9/internal/hashtag
github.com/go-redis/redis/v9/internal/hscan
github.com/go-redis/redis/v9/internal/pool
github.com/go-redis/redis/v9/internal/proto
github.com/go-redis/redis/v9/internal/rand
github.com/go-redis/redis/v9/internal/util
# github.com/go-sql-driver/mysql v1.7.0
## explicit; go 1.13
github.com/go-sql-driver/mysql
# github.com/goccy/go-json v0.9.11
## explicit; go 1.12
github.com/goccy/go-json
github.com/goccy/go-json/internal/decoder
github.com/goccy/go-json/internal/encoder
github.com/goccy/go-json/internal/encoder/vm
github.com/goccy/go-json/internal/encoder/vm_color
github.com/goccy/go-json/internal/encoder/vm_color_indent
github.com/goccy/go-json/internal/encoder/vm_indent
github.com/goccy/go-json/internal/errors
github.com/goccy/go-json/internal/runtime
# github.com/golang-jwt/jwt/v4 v4.4.2
## explicit; go 1.16
github.com/golang-jwt/jwt/v4
# github.com/golang-sql/civil v0.0.0-20220223132316-b832511892a9
## explicit
github.com/golang-sql/civil
# github.com/golang-sql/sqlexp v0.1.0
## explicit; go 1.16
github.com/golang-sql/sqlexp
# github.com/golang/freetype v0.0.0-20170609003504-e2365dfdc4a0
## explicit
github.com/golang/freetype
github.com/golang/freetype/raster
github.com/golang/freetype/truetype
# github.com/golang/protobuf v1.5.2
## explicit; go 1.9
github.com/golang/protobuf/proto
github.com/golang/protobuf/ptypes/timestamp
# github.com/golang/snappy v0.0.1
## explicit
github.com/golang/snappy
# github.com/google/uuid v1.3.0
## explicit
github.com/google/uuid
# github.com/gorilla/websocket v1.4.2
## explicit; go 1.12
github.com/gorilla/websocket
# github.com/henrylee2cn/ameda v1.5.1
## explicit; go 1.13
github.com/henrylee2cn/ameda
# github.com/huaweicloud/huaweicloud-sdk-go-obs v3.22.11+incompatible
## explicit
github.com/huaweicloud/huaweicloud-sdk-go-obs/obs
# github.com/imdario/mergo v0.3.9
## explicit
github.com/imdario/mergo
# github.com/inconshreveable/mousetrap v1.0.1
## explicit; go 1.18
github.com/inconshreveable/mousetrap
# github.com/jackc/chunkreader/v2 v2.0.1
## explicit; go 1.12
github.com/jackc/chunkreader/v2
# github.com/jackc/pgconn v1.13.0
## explicit; go 1.12
github.com/jackc/pgconn
github.com/jackc/pgconn/internal/ctxwatch
# github.com/jackc/pgio v1.0.0
## explicit; go 1.12
github.com/jackc/pgio
# github.com/jackc/pgpassfile v1.0.0
## explicit; go 1.12
github.com/jackc/pgpassfile
# github.com/jackc/pgproto3/v2 v2.3.1
## explicit; go 1.12
github.com/jackc/pgproto3/v2
# github.com/jackc/pgservicefile v0.0.0-20221227161230-091c0ba34f0a
## explicit; go 1.14
github.com/jackc/pgservicefile
# github.com/jackc/pgx/v5 v5.2.0
## explicit; go 1.18
github.com/jackc/pgx/v5
github.com/jackc/pgx/v5/internal/anynil
github.com/jackc/pgx/v5/internal/iobufpool
github.com/jackc/pgx/v5/internal/nbconn
github.com/jackc/pgx/v5/internal/pgio
github.com/jackc/pgx/v5/internal/sanitize
github.com/jackc/pgx/v5/internal/stmtcache
github.com/jackc/pgx/v5/pgconn
github.com/jackc/pgx/v5/pgconn/internal/ctxwatch
github.com/jackc/pgx/v5/pgproto3
github.com/jackc/pgx/v5/pgtype
github.com/jackc/pgx/v5/stdlib
# github.com/jinzhu/inflection v1.0.0
## explicit
github.com/jinzhu/inflection
# github.com/jinzhu/now v1.1.5
## explicit; go 1.12
github.com/jinzhu/now
# github.com/json-iterator/go v1.1.12
## explicit; go 1.12
github.com/json-iterator/go
# github.com/kballard/go-shellquote v0.0.0-20180428030007-95032a82bc51
## explicit
github.com/kballard/go-shellquote
# github.com/leodido/go-urn v1.2.1
## explicit; go 1.13
github.com/leodido/go-urn
# github.com/lufia/plan9stats v0.0.0-20211012122336-39d0f177ccd0
## explicit; go 1.16
github.com/lufia/plan9stats
# github.com/mattn/go-colorable v0.1.7
## explicit; go 1.13
github.com/mattn/go-colorable
# github.com/mattn/go-isatty v0.0.16
## explicit; go 1.15
github.com/mattn/go-isatty
# github.com/mattn/go-sqlite3 v1.14.15
## explicit; go 1.16
github.com/mattn/go-sqlite3
# github.com/mattn/goveralls v0.0.2
## explicit
github.com/mattn/goveralls
# github.com/matttproud/golang_protobuf_extensions v1.0.1
## explicit
github.com/matttproud/golang_protobuf_extensions/pbutil
# github.com/mgutz/ansi v0.0.0-20170206155736-9520e82c474b
## explicit
github.com/mgutz/ansi
# github.com/microsoft/go-mssqldb v0.19.0
## explicit; go 1.13
github.com/microsoft/go-mssqldb
github.com/microsoft/go-mssqldb/integratedauth
github.com/microsoft/go-mssqldb/integratedauth/ntlm
github.com/microsoft/go-mssqldb/integratedauth/winsspi
github.com/microsoft/go-mssqldb/internal/cp
github.com/microsoft/go-mssqldb/internal/decimal
github.com/microsoft/go-mssqldb/internal/querytext
github.com/microsoft/go-mssqldb/msdsn
# github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd
## explicit
github.com/modern-go/concurrent
# github.com/modern-go/reflect2 v1.0.2
## explicit; go 1.12
github.com/modern-go/reflect2
# github.com/mojocn/base64Captcha v1.3.1
## explicit; go 1.12
github.com/mojocn/base64Captcha
# github.com/mssola/user_agent v0.5.3
## explicit; go 1.13
github.com/mssola/user_agent
# github.com/nsqio/go-nsq v1.1.0
## explicit; go 1.11
github.com/nsqio/go-nsq
# github.com/nyaruka/phonenumbers v1.0.55
## explicit; go 1.13
github.com/nyaruka/phonenumbers
# github.com/opentracing/opentracing-go v1.2.0
## explicit; go 1.14
github.com/opentracing/opentracing-go
github.com/opentracing/opentracing-go/log
# github.com/pelletier/go-toml/v2 v2.0.6
## explicit; go 1.16
github.com/pelletier/go-toml/v2
github.com/pelletier/go-toml/v2/internal/characters
github.com/pelletier/go-toml/v2/internal/danger
github.com/pelletier/go-toml/v2/internal/tracker
github.com/pelletier/go-toml/v2/unstable
# github.com/pkg/errors v0.9.1
## explicit
github.com/pkg/errors
# github.com/power-devops/perfstat v0.0.0-20210106213030-5aafc221ea8c
## explicit; go 1.14
github.com/power-devops/perfstat
# github.com/pquerna/otp v1.4.0
## explicit; go 1.12
github.com/pquerna/otp
github.com/pquerna/otp/hotp
github.com/pquerna/otp/internal
github.com/pquerna/otp/totp
# github.com/prometheus/client_golang v1.14.0
## explicit; go 1.17
github.com/prometheus/client_golang/prometheus
github.com/prometheus/client_golang/prometheus/internal
github.com/prometheus/client_golang/prometheus/promhttp
# github.com/prometheus/client_model v0.3.0
## explicit; go 1.9
github.com/prometheus/client_model/go
# github.com/prometheus/common v0.37.0
## explicit; go 1.16
github.com/prometheus/common/expfmt
github.com/prometheus/common/internal/bitbucket.org/ww/goautoneg
github.com/prometheus/common/model
# github.com/prometheus/procfs v0.8.0
## explicit; go 1.17
github.com/prometheus/procfs
github.com/prometheus/procfs/internal/fs
github.com/prometheus/procfs/internal/util
# github.com/qiniu/go-sdk/v7 v7.14.0
## explicit; go 1.14
github.com/qiniu/go-sdk/v7
github.com/qiniu/go-sdk/v7/auth
github.com/qiniu/go-sdk/v7/auth/qbox
github.com/qiniu/go-sdk/v7/client
github.com/qiniu/go-sdk/v7/conf
github.com/qiniu/go-sdk/v7/internal/freezer
github.com/qiniu/go-sdk/v7/internal/hostprovider
github.com/qiniu/go-sdk/v7/internal/log
github.com/qiniu/go-sdk/v7/reqid
github.com/qiniu/go-sdk/v7/storage
# github.com/robfig/cron/v3 v3.0.1
## explicit; go 1.12
github.com/robfig/cron/v3
# github.com/russross/blackfriday/v2 v2.1.0
## explicit
github.com/russross/blackfriday/v2
# github.com/shamsher31/goimgext v1.0.0
## explicit
github.com/shamsher31/goimgext
# github.com/shirou/gopsutil/v3 v3.23.1
## explicit; go 1.15
github.com/shirou/gopsutil/v3/cpu
github.com/shirou/gopsutil/v3/disk
github.com/shirou/gopsutil/v3/host
github.com/shirou/gopsutil/v3/internal/common
github.com/shirou/gopsutil/v3/load
github.com/shirou/gopsutil/v3/mem
github.com/shirou/gopsutil/v3/net
github.com/shirou/gopsutil/v3/process
# github.com/spf13/cast v1.5.0
## explicit; go 1.18
github.com/spf13/cast
# github.com/spf13/cobra v1.6.1
## explicit; go 1.15
github.com/spf13/cobra
# github.com/spf13/pflag v1.0.5
## explicit; go 1.12
github.com/spf13/pflag
# github.com/tklauser/go-sysconf v0.3.11
## explicit; go 1.13
github.com/tklauser/go-sysconf
# github.com/tklauser/numcpus v0.6.0
## explicit; go 1.13
github.com/tklauser/numcpus
# github.com/tsuyoshiwada/go-gitcmd v0.0.0-20180205145712-5f1f5f9475df
## explicit
github.com/tsuyoshiwada/go-gitcmd
# github.com/ugorji/go/codec v1.2.7
## explicit; go 1.11
github.com/ugorji/go/codec
# github.com/unrolled/secure v1.13.0
## explicit; go 1.13
github.com/unrolled/secure
# github.com/urfave/cli v1.22.1
## explicit; go 1.11
github.com/urfave/cli
# github.com/yusufpapurcu/wmi v1.2.2
## explicit; go 1.16
github.com/yusufpapurcu/wmi
# go.uber.org/atomic v1.10.0
## explicit; go 1.18
go.uber.org/atomic
# go.uber.org/multierr v1.5.0
## explicit; go 1.12
go.uber.org/multierr
# go.uber.org/zap v1.13.0
## explicit; go 1.13
go.uber.org/zap
go.uber.org/zap/buffer
go.uber.org/zap/internal/bufferpool
go.uber.org/zap/internal/color
go.uber.org/zap/internal/exit
go.uber.org/zap/zapcore
# golang.org/x/crypto v0.6.0
## explicit; go 1.17
golang.org/x/crypto/bcrypt
golang.org/x/crypto/blowfish
golang.org/x/crypto/md4
golang.org/x/crypto/pbkdf2
golang.org/x/crypto/scrypt
golang.org/x/crypto/sha3
# golang.org/x/image v0.0.0-20220413100746-70e8d0d3baa9
## explicit; go 1.12
golang.org/x/image/bmp
golang.org/x/image/ccitt
golang.org/x/image/font
golang.org/x/image/math/fixed
golang.org/x/image/tiff
golang.org/x/image/tiff/lzw
# golang.org/x/lint v0.0.0-20200302205851-738671d3881b
## explicit; go 1.11
golang.org/x/lint
golang.org/x/lint/golint
# golang.org/x/net v0.6.0
## explicit; go 1.17
golang.org/x/net/http/httpguts
golang.org/x/net/http2
golang.org/x/net/http2/h2c
golang.org/x/net/http2/hpack
golang.org/x/net/idna
# golang.org/x/sync v0.0.0-20220923202941-7f9b1623fab7
## explicit
golang.org/x/sync/singleflight
# golang.org/x/sys v0.5.0
## explicit; go 1.17
golang.org/x/sys/cpu
golang.org/x/sys/internal/unsafeheader
golang.org/x/sys/unix
golang.org/x/sys/windows
golang.org/x/sys/windows/registry
# golang.org/x/text v0.7.0
## explicit; go 1.17
golang.org/x/text/cases
golang.org/x/text/internal
golang.org/x/text/internal/language
golang.org/x/text/internal/language/compact
golang.org/x/text/internal/tag
golang.org/x/text/language
golang.org/x/text/runes
golang.org/x/text/secure/bidirule
golang.org/x/text/secure/precis
golang.org/x/text/transform
golang.org/x/text/unicode/bidi
golang.org/x/text/unicode/norm
golang.org/x/text/width
# golang.org/x/time v0.0.0-20191024005414-555d28b269f0
## explicit
golang.org/x/time/rate
# golang.org/x/tools v0.1.12
## explicit; go 1.18
golang.org/x/tools/cover
golang.org/x/tools/go/ast/astutil
golang.org/x/tools/go/gcexportdata
golang.org/x/tools/go/internal/gcimporter
golang.org/x/tools/go/internal/pkgbits
golang.org/x/tools/internal/typeparams
# google.golang.org/protobuf v1.28.1
## explicit; go 1.11
google.golang.org/protobuf/encoding/prototext
google.golang.org/protobuf/encoding/protowire
google.golang.org/protobuf/internal/descfmt
google.golang.org/protobuf/internal/descopts
google.golang.org/protobuf/internal/detrand
google.golang.org/protobuf/internal/encoding/defval
google.golang.org/protobuf/internal/encoding/messageset
google.golang.org/protobuf/internal/encoding/tag
google.golang.org/protobuf/internal/encoding/text
google.golang.org/protobuf/internal/errors
google.golang.org/protobuf/internal/filedesc
google.golang.org/protobuf/internal/filetype
google.golang.org/protobuf/internal/flags
google.golang.org/protobuf/internal/genid
google.golang.org/protobuf/internal/impl
google.golang.org/protobuf/internal/order
google.golang.org/protobuf/internal/pragma
google.golang.org/protobuf/internal/set
google.golang.org/protobuf/internal/strs
google.golang.org/protobuf/internal/version
google.golang.org/protobuf/proto
google.golang.org/protobuf/reflect/protodesc
google.golang.org/protobuf/reflect/protoreflect
google.golang.org/protobuf/reflect/protoregistry
google.golang.org/protobuf/runtime/protoiface
google.golang.org/protobuf/runtime/protoimpl
google.golang.org/protobuf/types/descriptorpb
google.golang.org/protobuf/types/known/timestamppb
# gopkg.in/AlecAivazis/survey.v1 v1.8.5
## explicit
gopkg.in/AlecAivazis/survey.v1
gopkg.in/AlecAivazis/survey.v1/core
gopkg.in/AlecAivazis/survey.v1/terminal
# gopkg.in/kyokomi/emoji.v1 v1.5.1
## explicit
gopkg.in/kyokomi/emoji.v1
# gopkg.in/yaml.v2 v2.4.0
## explicit; go 1.15
gopkg.in/yaml.v2
# gorm.io/driver/mysql v1.4.6
## explicit; go 1.14
gorm.io/driver/mysql
# gorm.io/driver/postgres v1.4.7
## explicit; go 1.14
gorm.io/driver/postgres
# gorm.io/driver/sqlite v1.4.4
## explicit; go 1.14
gorm.io/driver/sqlite
# gorm.io/driver/sqlserver v1.4.2
## explicit; go 1.14
gorm.io/driver/sqlserver
# gorm.io/gorm v1.24.5
## explicit; go 1.16
gorm.io/gorm
gorm.io/gorm/callbacks
gorm.io/gorm/clause
gorm.io/gorm/logger
gorm.io/gorm/migrator
gorm.io/gorm/schema
gorm.io/gorm/utils
# gorm.io/plugin/dbresolver v1.3.0
## explicit; go 1.14
gorm.io/plugin/dbresolver
