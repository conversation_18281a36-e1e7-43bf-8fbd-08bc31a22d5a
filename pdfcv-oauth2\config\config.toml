# 1. 只有使用toml配置文件时，才支持环境变量注入
# 2. 环境变量注入需要逐行扫描配置文件，因此只有存在于配置文件的配置项才会进行检测
# 替换规则示例:
# [table1.table2.table3]
# key1 = value1
# 如果配置环境变量 PCV_TABLE3_KEY1, 那么配置将会使用环境变量而非来自配置文件的值
[openwps]
server_port = 8000
oauth_wps_api_base = "https://portal.kwps.cn"
oauth_wps_api_login = "/api/access/wpsopen/oauth2/authorize?appid=e7294019-6805-11ee-aab7-0abe8d1c9619&response_type=code&scope=user_info&redirect_uri=http://local-auth.wps.cn:8000/api/v1/callback&state="
oauth_wps_api_token = "/api/access/wpsopen/oauth2/token"
oauth_wps_api_userinfo = "/api/access/oauth2/user"
# 统一身份认证
appid = "XXX"
appkey = "XXX"
# 动态令牌
auth2appid = "xxx"
auth2appkey = "xxx"
# 是否开始动态令牌
switch_oauth2 = true
# token name
token_timeout = 86400
# // 双因子验证成功 token 有效期 xxx 秒 设置为0 关闭浏览器失效
token_name = "pdfcv-auth2-token"