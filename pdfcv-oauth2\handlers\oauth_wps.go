package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"pdfcv-oauth2/httputils"
	"strconv"
	"strings"
	"text/template"
	"time"

	"ksogit.kingsoft.net/security-pub/mfasdk"
)

func oauthWPSLogin(w http.ResponseWriter, r *http.Request) {
	u := fmt.Sprintf("%s%s%s", openWpsCfg.OauthWpsApiBase, openWpsCfg.OauthWpsApiLogin, r.<PERSON>alue("url"))
	fmt.Println("oauthWPSLogin_url:", u)
	http.Redirect(w, r, u, http.StatusTemporaryRedirect)
}

func oauthWPSAuth(w http.ResponseWriter, r *http.Request) {
	// Read oauthState from Cookie
	cookie, err := r.<PERSON>ie(openWpsCfg.TokenName)

	if err == http.ErrNoCookie {
		w.WriteHeader(http.StatusUnauthorized)
		w.<PERSON><PERSON>().Set("Content-Type", "application/json")
		resp := make(map[string]string)
		resp["message"] = "Unauthorized"
		jsonResp, err := json.Marshal(resp)
		if err != nil {
			log.Fatalf("Error happened in JSON marshal. Err: %s", err)
		}
		fmt.Println("oauthWPSAuth_no_cookie:", string(jsonResp))
		w.Write(jsonResp)
		return
	} else if err != nil {
		w.WriteHeader(http.StatusUnauthorized)
		w.Header().Set("Content-Type", "application/json")
		resp := make(map[string]string)
		resp["message"] = "Unauthorized"
		jsonResp, err := json.Marshal(resp)
		if err != nil {
			log.Fatalf("Error happened in JSON marshal. Err: %s", err)
		}
		w.Write(jsonResp)
		return
	} else if cookie.Value == "" {
		w.WriteHeader(http.StatusUnauthorized)
		w.Header().Set("Content-Type", "application/json")
		resp := make(map[string]string)
		resp["message"] = "Unauthorized"
		jsonResp, err := json.Marshal(resp)
		if err != nil {
			log.Fatalf("Error happened in JSON marshal. Err: %s", err)
		}
		fmt.Println("oauthWPSAuth_cookie_value:", string(jsonResp))
		w.Write(jsonResp)
		return
	}
	// cookie 存在
	// 刷新token
	// refreshTokenFromToken(w, r, cookie.Value)
	fmt.Fprintln(w, "Cookie value:", cookie.Value)
}

func oauthWPSCallback(w http.ResponseWriter, r *http.Request) {
	// Read oauthState from Cookie
	fmt.Println("callbackstart")
	code := r.FormValue("code")
	if code == "" {
		log.Printf("missing 'code' parameter in request")
		return
	}
	wps_uid, access_token, expires_in, refresh_token, _ := getUserDataFromWPS(w, code)
	// access_token_拼接
	access_token = access_token + "|" + strconv.FormatInt(expires_in, 10) + "|" + refresh_token
	state := r.FormValue("state")
	if openWpsCfg.SwitchOauth2 {
		redirectAuth2Html(w, wps_uid, access_token, state, "")
		fmt.Println("callback_SwitchOauth2_state:", state)
		return
	}
	generateStateOauthCookie(w, openWpsCfg.TokenTimeOut, access_token)
	fmt.Println("openWpsCfg.TokenTimeOut", openWpsCfg.TokenTimeOut)
	fmt.Println("w:", w)
	fmt.Println("access_token", access_token)
	http.Redirect(w, r, state, http.StatusMovedPermanently)
	fmt.Println("after redirect  http.StatusMovedPermanently", http.StatusMovedPermanently)
	fmt.Println("w:", w)
	fmt.Println("r", r)
	fmt.Println("callback_state2:", state)
	// fmt.Fprintf(w, "wps_uid: %s%s\n", strconv.FormatInt(wps_uid, 10), state)
}

func redirectAuth2Html(w http.ResponseWriter, wps_uid int64, token string, state string, msg string) {
	// 加载HTML模板文件
	tmpl, err := template.ParseFiles("./templates/index.html")
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	// 将参数传递给模板
	data := struct {
		WPSUID  string
		TOKEN   string
		URL     string
		MESSAGE string
	}{
		WPSUID:  strconv.FormatInt(wps_uid, 10),
		TOKEN:   token,
		URL:     state,
		MESSAGE: msg,
	}

	// 渲染HTML模板
	err = tmpl.Execute(w, data)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
	}
}

// set Cookie
func generateStateOauthCookie(w http.ResponseWriter, expiration int64, token string) {
	now := time.Now()
	etime := now.Add(time.Duration(expiration) * time.Second)
	cookie := &http.Cookie{
		Name:    openWpsCfg.TokenName,
		Value:   token,
		Expires: etime,      // 设置 cookie 过期时间，这里设置为 24 小时后过期
		Path:    "/",        // 设置 cookie 的路径，这里设置为根路径
		Domain:  ".kso.net", // 设置 cookie 的路径，这里设置为根路径
	}

	if err := validateCookie(cookie); err != nil {
		log.Printf("Cookie 校验失败，无法设置: %v, 详细信息: %+v", err, cookie)
		return
	}

	http.SetCookie(w, cookie) // 将 cookie 设置到响应中
}

func validateCookie(c *http.Cookie) error {
	// 1. 名称非空且符合 RFC 6265（不能含非法字符）
	if c.Name == "" {
		return fmt.Errorf("Cookie 名称为空")
	}
	// 非法字符参考：https://datatracker.ietf.org/doc/html/rfc6265#section-4.1.1
	if strings.ContainsAny(c.Name, " \t\n\r\000\\\"(),/:;<=>?@[]{}") {
		return fmt.Errorf("Cookie 名称含非法字符: %s", c.Name)
	}

	// 2. 其他可选校验（如 Value 长度、Domain 格式等）
	// 如需严格校验 Domain，可结合 net.ParseIP 或正则匹配域名规则

	return nil
}

// get UserInfo
func getUserDataFromWPS(w http.ResponseWriter, code string) (int64, string, int64, string, error) {
	// Use code to get token and get user info from 	WPS.
	token, expires_in, refresh_token, err := getTokenFromWPSCode(code)
	if err != nil || token == "" {
		err = fmt.Errorf(fmt.Sprintf("get wps token error, %s", err.Error()))
		return 0, "", 0, "", err
	}
	wps_uid, err := getUserInfoFromWPSToken(token)
	if err != nil {
		return 0, "", 0, "", fmt.Errorf("get wps_uid wrong: %s", err.Error())
	}
	return wps_uid, token, expires_in, refresh_token, nil
}

// get Token
func getTokenFromWPSCode(code string) (string, int64, string, error) {
	sUrl := fmt.Sprintf("%s%s", openWpsCfg.OauthWpsApiBase, openWpsCfg.OauthWpsApiToken)
	args := make(url.Values, 3)
	args.Set("appid", openWpsCfg.Appid)
	args.Set("appkey", openWpsCfg.Appkey)
	args.Set("code", code)
	var result struct {
		Result int    `json:"result"`
		Token  Token  `json:"token"`
		Msg    string `json:"msg"`
	}
	err := httputils.Get(sUrl, &args, &result)
	if err != nil {
		fmt.Println("err", err.Error())
		return "", 0, "", err
	}
	return result.Token.AccessToken, result.Token.ExpiresIn, result.Token.RefreshToken, nil
}

type UserInfo struct {
	Nickname string `json:"username"`
	Avatar   string `json:"avatar"`
	Sex      string `json:"sex"`
	Openid   string `json:"openid"`
	Unionid  string `json:"unionid"`
	WPSUid   int64  `json:"wps_uid"`
}

// get UserInfo
func getUserInfoFromWPSToken(accessToken string) (int64, error) {

	sUrl := fmt.Sprintf("%s%s", openWpsCfg.OauthWpsApiBase, openWpsCfg.OauthWpsApiUserinfo)
	args := make(url.Values, 3)
	args.Set("appid", openWpsCfg.Appid)
	args.Set("access_token", accessToken)
	var userInfo UserInfo
	err := httputils.Get(sUrl, &args, &userInfo)
	if err != nil {
		return 0, err
	}
	return userInfo.WPSUid, nil
}

type Token struct {
	Appid        string `json:"appid"`         // appid
	ExpiresIn    int64  `json:"expires_in"`    // 有效期
	AccessToken  string `json:"access_token"`  // accesstoken
	RefreshToken string `json:"refresh_token"` // refreshtoken
}

// 双因子登录
func oauth2WPSLogin(w http.ResponseWriter, r *http.Request) {
	fmt.Println("oauth2WPSLogin")
	url := r.FormValue("url")
	twofactor := r.FormValue("twofactor")
	access_token := r.FormValue("access_token")
	wps_uid, _ := strconv.ParseInt(r.FormValue("wps_uid"), 10, 64)
	ak := openWpsCfg.Auth2appid
	sk := openWpsCfg.Auth2appkey
	client := mfasdk.New(mfasdk.WithAKSK(ak, sk))
	// 验证动态令牌
	ok, err := client.Verify(mfasdk.VerifyParam{
		WpsUID: wps_uid,
		Code:   twofactor,
	})
	fmt.Println(url)
	fmt.Println(wps_uid, "verify status is", ok)
	fmt.Println(err)
	if err != nil {
		// err = errors.New(fmt.Sprintf("twofactor validate code, %s", err.Error()))
		// 如果处理失败，设置错误消息
		errorMessage := fmt.Sprintf("twofactor validate code, %s", err.Error())
		// 使用模板引擎将错误消息渲染到HTML模板中
		redirectAuth2Html(w, wps_uid, access_token, url, errorMessage)
		return
	}
	if !ok {
		// 如果处理失败，设置错误消息
		errorMessage := fmt.Sprintf("invalid twofactor code, %s", "invalid twofactor code")

		// 使用模板引擎将错误消息渲染到HTML模板中
		redirectAuth2Html(w, wps_uid, access_token, url, errorMessage)
		return
		// err = errors.New(fmt.Sprintf("invalid twofactor code, %s", err.Error()))
	}
	// 双因子验证成功 token 有效期 xxx 秒
	generateStateOauthCookie(w, openWpsCfg.TokenTimeOut, access_token)
	fmt.Println("openWpsCfg.TokenTimeOut", openWpsCfg.TokenTimeOut)
	fmt.Println("w:", w)
	fmt.Println("access_token", access_token)
	http.Redirect(w, r, url, http.StatusMovedPermanently)
	fmt.Println("oauth2WPSLogin_url:", url)
}

func refreshTokenFromToken(w http.ResponseWriter, r *http.Request, token string) {
	fmt.Println("refreshTokenFromToken")
	sUrl := openWpsCfg.OauthWpsApiBase + "/api/access/wpsopen/oauth2/token/refresh"
	args := make(map[string]interface{}, 3)
	args["appid"] = openWpsCfg.Appid
	args["appkey"] = openWpsCfg.Appkey
	args["refresh_token"] = token
	var result struct {
		Result int    `json:"result"`
		Token  Token  `json:"token"`
		Msg    string `json:"msg"`
	}
	err := httputils.Post(sUrl, args, &result)
	if err != nil {
		fmt.Println("refreshTokenFromToken failed ", err.Error())
		return
	}
	newToken := result.Token.AccessToken
	expires_in := result.Token.ExpiresIn
	fmt.Println("newToken", newToken)
	now := time.Now()
	etime := now.Add(time.Duration(expires_in) * time.Second)
	// 更新Cookie的值
	// 获取原始的Cookie
	cookie, _ := r.Cookie(openWpsCfg.TokenName)
	// 更新Cookie的值
	cookie.Value = newToken
	// 设置Cookie的过期时间
	cookie.Expires = etime
	// 设置Cookie的路径
	cookie.Path = "/"
	cookie.Domain = ".wps.cn"
	// 设置更新后的Cookie
	http.SetCookie(w, cookie)
	fmt.Println("Cookie updated")
}
