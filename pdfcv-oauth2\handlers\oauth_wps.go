package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"pdfcv-oauth2/httputils"
	"strconv"
	"strings"
	"text/template"
	"time"

	"ksogit.kingsoft.net/security-pub/mfasdk"
)

func oauthWPSLogin(w http.ResponseWriter, r *http.Request) {
	log.Printf("[OAuth登录] 开始处理OAuth登录重定向")
	log.Printf("[OAuth登录] 请求URL: %s", r.URL.String())
	log.Printf("[OAuth登录] 客户端IP: %s", r.RemoteAddr)
	log.Printf("[OAuth登录] User-Agent: %s", r.Header.Get("User-Agent"))

	originalUrl := r.FormValue("url")
	log.Printf("[OAuth登录] 原始URL参数: %s", originalUrl)

	// 验证重定向URL的安全性
	if originalUrl == "" {
		log.Printf("[OAuth登录] 警告: 原始URL为空")
	}

	// 构造WPS OAuth URL
	u := fmt.Sprintf("%s%s%s", openWpsCfg.OauthWpsApiBase, openWpsCfg.OauthWpsApiLogin, originalUrl)
	log.Printf("[OAuth登录] 构造的WPS OAuth URL: %s", u)
	log.Printf("[OAuth登录] URL长度: %d字符", len(u))

	// 检查响应状态
	log.Printf("[OAuth登录] 准备重定向 - 状态码: %d (StatusTemporaryRedirect)", http.StatusTemporaryRedirect)

	// 执行重定向前的最后检查
	if w.Header().Get("Content-Type") != "" {
		log.Printf("[OAuth登录] 警告: Content-Type已设置，可能影响重定向: %s", w.Header().Get("Content-Type"))
	}

	// 执行重定向
	log.Printf("[OAuth登录] 执行重定向到WPS OAuth服务器...")
	http.Redirect(w, r, u, http.StatusTemporaryRedirect)

	// 详细的重定向状态分析
	analyzeRedirectStatus(w, u, http.StatusTemporaryRedirect, "OAuth登录")
	log.Printf("[OAuth登录] OAuth登录重定向处理完成")
}

func oauthWPSAuth(w http.ResponseWriter, r *http.Request) {
	// Read oauthState from Cookie
	cookie, err := r.Cookie(openWpsCfg.TokenName)

	if err == http.ErrNoCookie {
		w.WriteHeader(http.StatusUnauthorized)
		w.Header().Set("Content-Type", "application/json")
		resp := make(map[string]string)
		resp["message"] = "Unauthorized"
		jsonResp, err := json.Marshal(resp)
		if err != nil {
			log.Fatalf("Error happened in JSON marshal. Err: %s", err)
		}
		w.Write(jsonResp)
		return
	} else if err != nil {
		w.WriteHeader(http.StatusUnauthorized)
		w.Header().Set("Content-Type", "application/json")
		resp := make(map[string]string)
		resp["message"] = "Unauthorized"
		jsonResp, err := json.Marshal(resp)
		if err != nil {
			log.Fatalf("Error happened in JSON marshal. Err: %s", err)
		}
		w.Write(jsonResp)
		return
	} else if cookie.Value == "" {
		w.WriteHeader(http.StatusUnauthorized)
		w.Header().Set("Content-Type", "application/json")
		resp := make(map[string]string)
		resp["message"] = "Unauthorized"
		jsonResp, err := json.Marshal(resp)
		if err != nil {
			log.Fatalf("Error happened in JSON marshal. Err: %s", err)
		}
		w.Write(jsonResp)
		return
	}
	// cookie 存在
	// 刷新token
	// refreshTokenFromToken(w, r, cookie.Value)
	fmt.Fprintln(w, "Cookie value:", cookie.Value)
}

func oauthWPSCallback(w http.ResponseWriter, r *http.Request) {
	log.Printf("[OAuth回调] 开始处理OAuth回调请求")
	log.Printf("[OAuth回调] 请求URL: %s", r.URL.String())
	log.Printf("[OAuth回调] 请求方法: %s", r.Method)
	log.Printf("[OAuth回调] 客户端IP: %s", r.RemoteAddr)
	log.Printf("[OAuth回调] User-Agent: %s", r.Header.Get("User-Agent"))
	log.Printf("[OAuth回调] Referer: %s", r.Header.Get("Referer"))

	// Read oauthState from Cookie
	code := r.FormValue("code")
	state := r.FormValue("state")

	log.Printf("[OAuth回调] 获取参数 - code: %s, state: %s", code, state)

	if code == "" {
		log.Printf("[OAuth回调] 错误: 缺少授权码参数")
		http.Error(w, "Missing authorization code", http.StatusBadRequest)
		return
	}

	if state == "" {
		log.Printf("[OAuth回调] 警告: 缺少state参数，可能影响重定向")
	}

	log.Printf("[OAuth回调] 开始获取用户数据...")
	wps_uid, access_token, expires_in, refresh_token, err := getUserDataFromWPS(w, code)
	if err != nil {
		log.Printf("[OAuth回调] 获取用户数据失败: %v", err)
		http.Error(w, "Failed to get user data", http.StatusInternalServerError)
		return
	}

	log.Printf("[OAuth回调] 用户数据获取成功 - wps_uid: %d, expires_in: %d", wps_uid, expires_in)

	// access_token_拼接
	access_token = access_token + "|" + strconv.FormatInt(expires_in, 10) + "|" + refresh_token
	log.Printf("[OAuth回调] Token拼接完成，总长度: %d", len(access_token))

	// 检查双因子认证设置
	if openWpsCfg.SwitchOauth2 {
		log.Printf("[OAuth回调] 双因子认证已开启，跳转到双因子页面")
		log.Printf("[OAuth回调] 双因子页面参数 - wps_uid: %d, state: %s", wps_uid, state)
		redirectAuth2Html(w, wps_uid, access_token, state, "")
		return
	}

	log.Printf("[OAuth回调] 双因子认证未开启，直接设置Cookie并重定向")
	log.Printf("[OAuth回调] Cookie设置参数 - timeout: %d秒, token长度: %d", openWpsCfg.TokenTimeOut, len(access_token))

	// 检查响应状态，确保可以设置Cookie
	if w.Header().Get("Content-Type") != "" {
		log.Printf("[OAuth回调] 警告: Content-Type已设置，可能影响Cookie: %s", w.Header().Get("Content-Type"))
	}

	generateStateOauthCookie(w, openWpsCfg.TokenTimeOut, access_token)
	log.Printf("[OAuth回调] Cookie设置完成")

	// 重定向前的验证
	if state == "" {
		log.Printf("[OAuth回调] 错误: 重定向URL为空，无法执行重定向")
		http.Error(w, "Missing redirect URL", http.StatusBadRequest)
		return
	}

	log.Printf("[OAuth回调] 准备重定向到原始URL: %s", state)
	log.Printf("[OAuth回调] 重定向状态码: %d (StatusMovedPermanently)", http.StatusMovedPermanently)
	log.Printf("[OAuth回调] URL长度: %d字符", len(state))

	// 执行重定向
	log.Printf("[OAuth回调] 执行重定向...")
	http.Redirect(w, r, state, http.StatusMovedPermanently)

	// 详细的重定向状态分析
	analyzeRedirectStatus(w, state, http.StatusMovedPermanently, "OAuth回调")
	log.Printf("[OAuth回调] OAuth回调处理完成")
}

func redirectAuth2Html(w http.ResponseWriter, wps_uid int64, token string, state string, msg string) {
	// 加载HTML模板文件
	tmpl, err := template.ParseFiles("./templates/index.html")
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	// 将参数传递给模板
	data := struct {
		WPSUID  string
		TOKEN   string
		URL     string
		MESSAGE string
	}{
		WPSUID:  strconv.FormatInt(wps_uid, 10),
		TOKEN:   token,
		URL:     state,
		MESSAGE: msg,
	}

	// 渲染HTML模板
	err = tmpl.Execute(w, data)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
	}
}

// set Cookie
func generateStateOauthCookie(w http.ResponseWriter, expiration int64, token string) {
	now := time.Now()
	etime := now.Add(time.Duration(expiration) * time.Second)
	cookie := &http.Cookie{
		Name:    openWpsCfg.TokenName,
		Value:   token,
		Expires: etime,      // 设置 cookie 过期时间，这里设置为 24 小时后过期
		Path:    "/",        // 设置 cookie 的路径，这里设置为根路径
		Domain:  ".kso.net", // 设置 cookie 的路径，这里设置为根路径
	}
	http.SetCookie(w, cookie) // 将 cookie 设置到响应中
}

// get UserInfo
func getUserDataFromWPS(w http.ResponseWriter, code string) (int64, string, int64, string, error) {
	// Use code to get token and get user info from 	WPS.
	token, expires_in, refresh_token, err := getTokenFromWPSCode(code)
	if err != nil || token == "" {
		err = fmt.Errorf(fmt.Sprintf("get wps token error, %s", err.Error()))
		return 0, "", 0, "", err
	}
	wps_uid, err := getUserInfoFromWPSToken(token)
	if err != nil {
		return 0, "", 0, "", fmt.Errorf("get wps_uid wrong: %s", err.Error())
	}
	return wps_uid, token, expires_in, refresh_token, nil
}

// get Token
func getTokenFromWPSCode(code string) (string, int64, string, error) {
	sUrl := fmt.Sprintf("%s%s", openWpsCfg.OauthWpsApiBase, openWpsCfg.OauthWpsApiToken)
	args := make(url.Values, 3)
	args.Set("appid", openWpsCfg.Appid)
	args.Set("appkey", openWpsCfg.Appkey)
	args.Set("code", code)
	var result struct {
		Result int    `json:"result"`
		Token  Token  `json:"token"`
		Msg    string `json:"msg"`
	}
	err := httputils.Get(sUrl, &args, &result)
	if err != nil {
		fmt.Println("err", err.Error())
		return "", 0, "", err
	}
	return result.Token.AccessToken, result.Token.ExpiresIn, result.Token.RefreshToken, nil
}

type UserInfo struct {
	Nickname string `json:"username"`
	Avatar   string `json:"avatar"`
	Sex      string `json:"sex"`
	Openid   string `json:"openid"`
	Unionid  string `json:"unionid"`
	WPSUid   int64  `json:"wps_uid"`
}

// get UserInfo
func getUserInfoFromWPSToken(accessToken string) (int64, error) {

	sUrl := fmt.Sprintf("%s%s", openWpsCfg.OauthWpsApiBase, openWpsCfg.OauthWpsApiUserinfo)
	args := make(url.Values, 3)
	args.Set("appid", openWpsCfg.Appid)
	args.Set("access_token", accessToken)
	var userInfo UserInfo
	err := httputils.Get(sUrl, &args, &userInfo)
	if err != nil {
		return 0, err
	}
	return userInfo.WPSUid, nil
}

type Token struct {
	Appid        string `json:"appid"`         // appid
	ExpiresIn    int64  `json:"expires_in"`    // 有效期
	AccessToken  string `json:"access_token"`  // accesstoken
	RefreshToken string `json:"refresh_token"` // refreshtoken
}

// 双因子登录
func oauth2WPSLogin(w http.ResponseWriter, r *http.Request) {
	log.Printf("[双因子认证] 开始处理双因子认证提交")
	log.Printf("[双因子认证] 请求URL: %s", r.URL.String())
	log.Printf("[双因子认证] 请求方法: %s", r.Method)
	log.Printf("[双因子认证] 客户端IP: %s", r.RemoteAddr)
	log.Printf("[双因子认证] User-Agent: %s", r.Header.Get("User-Agent"))

	url := r.FormValue("url")
	twofactor := r.FormValue("twofactor")
	access_token := r.FormValue("access_token")
	wps_uid_str := r.FormValue("wps_uid")

	log.Printf("[双因子认证] 获取参数 - url: %s, twofactor: %s, wps_uid: %s", url, twofactor, wps_uid_str)
	log.Printf("[双因子认证] access_token长度: %d", len(access_token))

	wps_uid, err := strconv.ParseInt(wps_uid_str, 10, 64)
	if err != nil {
		log.Printf("[双因子认证] 错误: 解析wps_uid失败: %v", err)
		http.Error(w, "Invalid wps_uid", http.StatusBadRequest)
		return
	}

	if twofactor == "" {
		log.Printf("[双因子认证] 错误: 双因子验证码为空")
		errorMessage := "双因子验证码不能为空"
		redirectAuth2Html(w, wps_uid, access_token, url, errorMessage)
		return
	}

	ak := openWpsCfg.Auth2appid
	sk := openWpsCfg.Auth2appkey
	log.Printf("[双因子认证] 初始化MFA客户端 - appid: %s", ak)

	client := mfasdk.New(mfasdk.WithAKSK(ak, sk))

	// 验证动态令牌
	log.Printf("[双因子认证] 开始验证动态令牌 - wps_uid: %d, code: %s", wps_uid, twofactor)
	ok, err := client.Verify(mfasdk.VerifyParam{
		WpsUID: wps_uid,
		Code:   twofactor,
	})

	log.Printf("[双因子认证] 验证结果 - wps_uid: %d, 验证状态: %t, 错误: %v", wps_uid, ok, err)

	if err != nil {
		log.Printf("[双因子认证] 验证过程出错: %v", err)
		errorMessage := fmt.Sprintf("双因子验证失败: %s", err.Error())
		log.Printf("[双因子认证] 返回错误页面: %s", errorMessage)
		redirectAuth2Html(w, wps_uid, access_token, url, errorMessage)
		return
	}

	if !ok {
		log.Printf("[双因子认证] 验证码无效 - wps_uid: %d", wps_uid)
		errorMessage := "双因子验证码无效，请重新输入"
		log.Printf("[双因子认证] 返回错误页面: %s", errorMessage)
		redirectAuth2Html(w, wps_uid, access_token, url, errorMessage)
		return
	}

	// 双因子验证成功
	log.Printf("[双因子认证] 验证成功 - wps_uid: %d", wps_uid)
	log.Printf("[双因子认证] 设置Cookie - timeout: %d秒, token长度: %d", openWpsCfg.TokenTimeOut, len(access_token))

	// 检查响应状态
	if w.Header().Get("Content-Type") != "" {
		log.Printf("[双因子认证] 警告: Content-Type已设置，可能影响Cookie: %s", w.Header().Get("Content-Type"))
	}

	generateStateOauthCookie(w, openWpsCfg.TokenTimeOut, access_token)
	log.Printf("[双因子认证] Cookie设置完成")

	// 重定向前的验证
	if url == "" {
		log.Printf("[双因子认证] 错误: 重定向URL为空，无法执行重定向")
		http.Error(w, "Missing redirect URL", http.StatusBadRequest)
		return
	}

	log.Printf("[双因子认证] 准备重定向到原始URL: %s", url)
	log.Printf("[双因子认证] 重定向状态码: %d (StatusMovedPermanently)", http.StatusMovedPermanently)
	log.Printf("[双因子认证] URL长度: %d字符", len(url))

	// 执行重定向
	log.Printf("[双因子认证] 执行重定向...")
	http.Redirect(w, r, url, http.StatusMovedPermanently)

	// 详细的重定向状态分析
	analyzeRedirectStatus(w, url, http.StatusMovedPermanently, "双因子认证")
	log.Printf("[双因子认证] 双因子认证重定向处理完成")
}

// 重定向状态检查和分析函数
func analyzeRedirectStatus(w http.ResponseWriter, targetURL string, statusCode int, context string) {
	log.Printf("[重定向分析] ========== %s 重定向状态分析 ==========", context)
	log.Printf("[重定向分析] 目标URL: %s", targetURL)
	log.Printf("[重定向分析] 状态码: %d", statusCode)
	log.Printf("[重定向分析] 状态码含义: %s", getStatusCodeMeaning(statusCode))

	// 检查URL有效性
	if targetURL == "" {
		log.Printf("[重定向分析] ❌ 错误: 目标URL为空")
		return
	}

	// 检查URL长度
	if len(targetURL) > 2048 {
		log.Printf("[重定向分析] ⚠️  警告: URL长度过长 (%d字符)，可能被某些浏览器截断", len(targetURL))
	}

	// 检查URL格式
	if !strings.HasPrefix(targetURL, "http://") && !strings.HasPrefix(targetURL, "https://") {
		log.Printf("[重定向分析] ⚠️  警告: URL不是完整的HTTP/HTTPS地址")
	}

	// 检查响应头
	location := w.Header().Get("Location")
	if location == "" {
		log.Printf("[重定向分析] ❌ 错误: Location响应头未设置")
	} else if location != targetURL {
		log.Printf("[重定向分析] ⚠️  警告: Location头与目标URL不匹配")
		log.Printf("[重定向分析] Location头: %s", location)
		log.Printf("[重定向分析] 目标URL: %s", targetURL)
	} else {
		log.Printf("[重定向分析] ✅ Location响应头设置正确")
	}

	// 检查其他可能影响重定向的响应头
	contentType := w.Header().Get("Content-Type")
	if contentType != "" {
		log.Printf("[重定向分析] ⚠️  警告: Content-Type已设置: %s，可能影响重定向", contentType)
	}

	contentLength := w.Header().Get("Content-Length")
	if contentLength != "" && contentLength != "0" {
		log.Printf("[重定向分析] ⚠️  警告: Content-Length已设置: %s，可能影响重定向", contentLength)
	}

	// 检查缓存相关头
	cacheControl := w.Header().Get("Cache-Control")
	if cacheControl != "" {
		log.Printf("[重定向分析] 📝 Cache-Control: %s", cacheControl)
	}

	log.Printf("[重定向分析] 所有响应头: %+v", w.Header())
	log.Printf("[重定向分析] ========== %s 重定向分析完成 ==========", context)
}

// 获取HTTP状态码含义
func getStatusCodeMeaning(code int) string {
	switch code {
	case 301:
		return "301 Moved Permanently - 永久重定向"
	case 302:
		return "302 Found - 临时重定向"
	case 303:
		return "303 See Other - 查看其他位置"
	case 307:
		return "307 Temporary Redirect - 临时重定向(保持请求方法)"
	case 308:
		return "308 Permanent Redirect - 永久重定向(保持请求方法)"
	default:
		return fmt.Sprintf("%d - 未知状态码", code)
	}
}

func refreshTokenFromToken(w http.ResponseWriter, r *http.Request, token string) {
	sUrl := openWpsCfg.OauthWpsApiBase + "/api/access/wpsopen/oauth2/token/refresh"
	args := make(map[string]interface{}, 3)
	args["appid"] = openWpsCfg.Appid
	args["appkey"] = openWpsCfg.Appkey
	args["refresh_token"] = token
	var result struct {
		Result int    `json:"result"`
		Token  Token  `json:"token"`
		Msg    string `json:"msg"`
	}
	err := httputils.Post(sUrl, args, &result)
	if err != nil {
		fmt.Println("refreshTokenFromToken failed ", err.Error())
		return
	}
	newToken := result.Token.AccessToken
	expires_in := result.Token.ExpiresIn
	fmt.Println("newToken", newToken)
	now := time.Now()
	etime := now.Add(time.Duration(expires_in) * time.Second)
	// 更新Cookie的值
	// 获取原始的Cookie
	cookie, _ := r.Cookie(openWpsCfg.TokenName)
	// 更新Cookie的值
	cookie.Value = newToken
	// 设置Cookie的过期时间
	cookie.Expires = etime
	// 设置Cookie的路径
	cookie.Path = "/"
	cookie.Domain = ".wps.cn"
	// 设置更新后的Cookie
	http.SetCookie(w, cookie)
	fmt.Println("Cookie updated")
}
