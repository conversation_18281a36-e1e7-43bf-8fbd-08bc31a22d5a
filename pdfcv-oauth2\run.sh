#!/bin/bash
docker rm pdfcv-oauth2
PORT="PCV_OPENWPS_SERVER_PORT=8000"
API_LOGIN="PCV_OPENWPS_OAUTH_WPS_API_LOGIN=/api/access/wpsopen/oauth2/authorize?appid=e7294019-6805-11ee-aab7-0abe8d1c9619&response_type=code&scope=user_info&redirect_uri=http://local-auth.kso.net:8000/api/v1/callback&state="
APPID="PCV_OPENWPS_APPID=b6e3d031-78b1-11f0-847c-2285ece1bd64"
APPKEY="PCV_OPENWPS_APPKEY=Q7jwo0KPnFc2eV3568zA1litB9TGUs4h"
AUTH2APPID="PCV_OPENWPS_AUTH2APPID=h46I8fo90Q1TzxPjqK73DHX2FtZJWw5B"
AUTH2APPKEY="PCV_OPENWPS_AUTH2APPKEY=Fz3l0m6tcRb1STiwy8Qn9s2d7N54Vqhj"

docker run -it -p 8000:8000  -e ${PORT}  -e ${API_LOGIN}  -e ${APPID}  -e ${APPKEY}  -e ${AUTH2APPID}  -e ${AUTH2APPKEY} --name pdfcv-oauth2 pdfcv-oauth2 bash